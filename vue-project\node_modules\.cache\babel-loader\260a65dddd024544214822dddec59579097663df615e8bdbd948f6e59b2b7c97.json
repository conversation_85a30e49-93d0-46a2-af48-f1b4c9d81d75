{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    attrs: {\n      id: \"app\"\n    }\n  }, [_c(\"el-container\", [_c(\"el-header\", {\n    staticClass: \"header\"\n  }, [_c(\"el-menu\", {\n    staticClass: \"el-menu-demo\",\n    attrs: {\n      \"default-active\": _vm.$route.path,\n      mode: \"horizontal\",\n      router: \"\",\n      \"background-color\": \"#545c64\",\n      \"text-color\": \"#fff\",\n      \"active-text-color\": \"#ffd04b\"\n    }\n  }, [_c(\"el-menu-item\", {\n    attrs: {\n      index: \"/\"\n    }\n  }, [_vm._v(\"首页\")]), _c(\"el-menu-item\", {\n    attrs: {\n      index: \"/about\"\n    }\n  }, [_vm._v(\"关于\")])], 1)], 1), _c(\"el-main\", {\n    staticClass: \"main\"\n  }, [_c(\"router-view\")], 1)], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "id", "staticClass", "$route", "path", "mode", "router", "index", "_v", "staticRenderFns", "_withStripped"], "sources": ["C:/work/testProduct/figma/vue-project/src/App.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { attrs: { id: \"app\" } },\n    [\n      _c(\n        \"el-container\",\n        [\n          _c(\n            \"el-header\",\n            { staticClass: \"header\" },\n            [\n              _c(\n                \"el-menu\",\n                {\n                  staticClass: \"el-menu-demo\",\n                  attrs: {\n                    \"default-active\": _vm.$route.path,\n                    mode: \"horizontal\",\n                    router: \"\",\n                    \"background-color\": \"#545c64\",\n                    \"text-color\": \"#fff\",\n                    \"active-text-color\": \"#ffd04b\",\n                  },\n                },\n                [\n                  _c(\"el-menu-item\", { attrs: { index: \"/\" } }, [\n                    _vm._v(\"首页\"),\n                  ]),\n                  _c(\"el-menu-item\", { attrs: { index: \"/about\" } }, [\n                    _vm._v(\"关于\"),\n                  ]),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _c(\"el-main\", { staticClass: \"main\" }, [_c(\"router-view\")], 1),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAM;EAAE,CAAC,EACxB,CACEH,EAAE,CACA,cAAc,EACd,CACEA,EAAE,CACA,WAAW,EACX;IAAEI,WAAW,EAAE;EAAS,CAAC,EACzB,CACEJ,EAAE,CACA,SAAS,EACT;IACEI,WAAW,EAAE,cAAc;IAC3BF,KAAK,EAAE;MACL,gBAAgB,EAAEH,GAAG,CAACM,MAAM,CAACC,IAAI;MACjCC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE,EAAE;MACV,kBAAkB,EAAE,SAAS;MAC7B,YAAY,EAAE,MAAM;MACpB,mBAAmB,EAAE;IACvB;EACF,CAAC,EACD,CACER,EAAE,CAAC,cAAc,EAAE;IAAEE,KAAK,EAAE;MAAEO,KAAK,EAAE;IAAI;EAAE,CAAC,EAAE,CAC5CV,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,EACFV,EAAE,CAAC,cAAc,EAAE;IAAEE,KAAK,EAAE;MAAEO,KAAK,EAAE;IAAS;EAAE,CAAC,EAAE,CACjDV,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDV,EAAE,CAAC,SAAS,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CAACJ,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAC/D,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIW,eAAe,GAAG,EAAE;AACxBb,MAAM,CAACc,aAAa,GAAG,IAAI;AAE3B,SAASd,MAAM,EAAEa,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}