{"name": "http-parser-js", "version": "0.5.10", "description": "A pure JS HTTP parser for node.", "main": "http-parser.js", "types": "http-parser.d.ts", "scripts": {"test": "python tests/test.py && node tests/iojs/test-http-parser-durability.js", "testv12": "python tests/test.py --node-args=\"--http-parser=legacy\" && node --http-parser=legacy tests/iojs/test-http-parser-durability.js"}, "repository": {"type": "git", "url": "git://github.com/creationix/http-parser-js.git"}, "files": ["http-parser.js", "http-parser.d.ts"], "keywords": ["http"], "author": "<PERSON> (https://github.com/creationix)", "contributors": ["<PERSON><PERSON> (https://github.com/Jimbly)", "<PERSON> (https://github.com/lrowe)", "<PERSON> (https://github.com/jscissr)", "<PERSON> (https://github.com/paulrutter)"], "license": "MIT"}