<template>
  <div class="save-earth-home">
    <!-- Background with gradient -->
    <div class="background-gradient"></div>

    <!-- Large background text -->
    <div class="background-text">SAVE<br />EARTH</div>

    <!-- Main content container -->
    <div class="main-container">
      <!-- Navigation Header -->
      <header class="navigation">
        <div class="nav-left">
          <span class="earth-emoji">🌎</span>
          <span class="brand-text">Save Earth</span>
          <span class="earth-emoji-overlay">🌎</span>
        </div>
        <nav class="nav-right">
          <a href="#" class="nav-link">About</a>
          <a href="#" class="nav-link">Articles</a>
          <a href="#" class="nav-link">For Business</a>
          <a href="#" class="nav-link">Showcase</a>
        </nav>
      </header>

      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">Mother Earth</h1>
          <p class="hero-subtitle">We help you live carbon neutral</p>

          <!-- Calculate Impact Button -->
          <button class="calculate-btn" @click="calculateImpact">
            Calculate Impact
          </button>
        </div>

        <!-- Hero Image -->
        <div class="hero-image">
          <div class="earth-image-container">
            <div class="earth-image"></div>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section class="features-section">
        <!-- Feature 1: Understand Emission -->
        <div class="feature-card feature-left">
          <div class="feature-content">
            <h3 class="feature-title">Understand Emission</h3>
            <p class="feature-description">
              Use our calculation powered by data from world bank to estimate
              your emission
            </p>
            <div class="discover-more" @click="discoverMore('emission')">
              <span class="discover-text">Discover More</span>
              <div class="discover-button">
                <div class="arrow-icon">→</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Feature 2: Support Climate Projects -->
        <div class="feature-card feature-right">
          <div class="feature-content">
            <h3 class="feature-title">Support Climate Projects</h3>
            <p class="feature-description">
              Sign up and and fund high impact carbon offsetting & plastic
              recycling initiatives.
            </p>
            <div class="discover-more" @click="discoverMore('projects')">
              <span class="discover-text">Discover More</span>
              <div class="discover-button">
                <div class="arrow-icon">→</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Decorative Elements -->
      <div class="decorative-blur green-blur"></div>
      <div class="decorative-blur red-blur"></div>
    </div>

    <!-- Footer -->
    <footer class="footer">
      <span class="footer-text">www.nickelfox.com</span>
    </footer>
  </div>
</template>

<script>
export default {
  name: "HomePage",
  data() {
    return {
      // Data for the Save Earth page
    };
  },
  methods: {
    calculateImpact() {
      this.$message.success("Calculate Impact feature coming soon!");
      console.log("Calculate Impact clicked");
    },
    discoverMore(type) {
      this.$message.info(`Discover more about ${type}`);
      console.log(`Discover more clicked for: ${type}`);
    },
  },
};
</script>

<style scoped>
/* Main container */
.save-earth-home {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  font-family: "Inter", sans-serif;
}

/* Background gradient */
.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #889fa3 0%, #a5b6b9 100%);
  z-index: 1;
}

/* Large background text */
.background-text {
  position: absolute;
  top: -126px;
  left: -39px;
  font-family: "Inter", sans-serif;
  font-weight: 700;
  font-size: 600px;
  line-height: 1.21;
  letter-spacing: 7%;
  color: rgba(0, 24, 28, 0.08);
  z-index: 2;
  pointer-events: none;
}

/* Main container */
.main-container {
  position: relative;
  width: 1440px;
  height: 1117px;
  margin: 83px auto 0;
  background: #00181c;
  border-radius: 40px;
  z-index: 3;
  overflow: hidden;
}

/* Navigation */
.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px 80px;
  width: 100%;
  box-sizing: border-box;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.earth-emoji {
  font-size: 50px;
  line-height: 1;
}

.brand-text {
  font-family: "Amita", serif;
  font-size: 32px;
  color: #ffffff;
  letter-spacing: 4%;
}

.earth-emoji-overlay {
  position: absolute;
  left: 0;
  top: 6px;
  font-size: 50px;
  line-height: 1;
}

.nav-right {
  display: flex;
  gap: 60px;
  align-items: center;
}

.nav-link {
  font-family: "Secular One", sans-serif;
  font-size: 16px;
  color: #ffffff;
  text-decoration: none;
  letter-spacing: 4%;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #1df659;
}

/* Hero Section */
.hero-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 80px;
  margin-top: 55px;
}

.hero-content {
  flex: 1;
  max-width: 500px;
}

.hero-title {
  font-family: "Secular One", sans-serif;
  font-size: 205px;
  line-height: 1.45;
  color: #ffffff;
  margin: 0;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-family: "Inter", sans-serif;
  font-size: 30px;
  line-height: 1.5;
  color: #ffffff;
  letter-spacing: 4%;
  margin: 0;
  margin-bottom: 40px;
  max-width: 262px;
}

.calculate-btn {
  background: #1df659;
  border: none;
  border-radius: 0;
  padding: 22px 30px;
  font-family: "CircularXX", sans-serif;
  font-weight: 500;
  font-size: 24px;
  color: #00181c;
  cursor: pointer;
  transition: all 0.3s ease;
}

.calculate-btn:hover {
  background: #16d94a;
  transform: translateY(-2px);
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.earth-image-container {
  width: 609px;
  height: 602px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.earth-image {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #4caf50 0%, #2e7d32 50%, #1b5e20 100%);
  border-radius: 50%;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.earth-image::before {
  content: "🌍";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 200px;
  opacity: 0.8;
}

/* Features Section */
.features-section {
  position: relative;
  margin-top: 100px;
  padding: 0 80px;
}

.feature-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40px);
  padding: 40px;
  color: #ffffff;
}

.feature-left {
  left: 80px;
  bottom: -200px;
  width: 389px;
  height: 489px;
  border-radius: 0 89px 0 0;
}

.feature-right {
  right: 80px;
  bottom: -200px;
  width: 560px;
  height: 292px;
  border-radius: 0 89px 0 0;
}

.feature-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.feature-title {
  font-family: "Inter", sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 4%;
  text-transform: uppercase;
  color: #ffffff;
  margin: 0 0 20px 0;
}

.feature-description {
  font-family: "Inter", sans-serif;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 4%;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 40px 0;
  flex-grow: 1;
}

.discover-more {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.discover-more:hover {
  transform: translateX(5px);
}

.discover-text {
  font-family: "Inter", sans-serif;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 4%;
  color: #1df659;
}

.discover-button {
  width: 45px;
  height: 45px;
  background: rgba(29, 246, 89, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.discover-button::before {
  content: "";
  position: absolute;
  width: 37px;
  height: 37px;
  background: #1df659;
  border-radius: 50%;
  top: 4px;
  left: 4px;
}

.arrow-icon {
  position: relative;
  z-index: 1;
  color: #080808;
  font-size: 16px;
  font-weight: bold;
}

/* Decorative blur elements */
.decorative-blur {
  position: absolute;
  filter: blur(500px);
  pointer-events: none;
}

.green-blur {
  width: 215px;
  height: 415px;
  background: #087e69;
  left: 0;
  bottom: 0;
}

.red-blur {
  width: 215px;
  height: 415px;
  background: #d30202;
  right: 0;
  bottom: 200px;
}

/* Footer */
.footer {
  position: absolute;
  top: 0;
  right: 0;
  width: 351px;
  height: 63px;
  background: rgba(0, 0, 0, 0.11);
  backdrop-filter: blur(100px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.footer-text {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.6;
  letter-spacing: 10%;
  color: #000000;
}

/* Responsive adjustments */
@media (max-width: 1600px) {
  .save-earth-home {
    width: 100%;
    height: 100vh;
  }

  .main-container {
    width: 90%;
    max-width: 1440px;
    height: auto;
    min-height: 80vh;
  }

  .background-text {
    font-size: 400px;
  }

  .hero-title {
    font-size: 150px;
  }

  .feature-left,
  .feature-right {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
    margin: 20px 0;
    width: 100%;
    max-width: 500px;
  }

  .features-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 50px;
    position: relative;
  }
}

@media (max-width: 768px) {
  .navigation {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }

  .nav-right {
    gap: 20px;
  }

  .hero-section {
    flex-direction: column;
    padding: 0 20px;
    text-align: center;
  }

  .hero-title {
    font-size: 80px;
  }

  .hero-subtitle {
    font-size: 20px;
    max-width: none;
  }

  .earth-image-container {
    width: 300px;
    height: 300px;
  }

  .earth-image {
    width: 200px;
    height: 200px;
  }

  .earth-image::before {
    font-size: 100px;
  }
}
</style>
