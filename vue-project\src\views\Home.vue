<template>
  <div class="restaurant-home">
    <!-- Header -->
    <header class="header">
      <div class="header-content">
        <h1 class="logo">DINE IN FLORIDA</h1>
        <nav class="nav-menu">
          <a href="#" class="nav-item active">Home</a>
          <a href="#" class="nav-item">Restaurants</a>
          <a href="#" class="nav-item">Reservations</a>
        </nav>
        <div class="header-actions">
          <button class="login-btn">Login</button>
          <button class="signup-btn">Sign Up</button>
          <div class="profile-avatar"></div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Left Sidebar -->
      <aside class="sidebar">
        <!-- Promotional Banner -->
        <div class="promo-banner">
          <p class="promo-text">
            Automatically save 2% on your bill if you reserve your Table With
            DINE IN FLORIDA
          </p>
        </div>

        <!-- All Locations Section -->
        <div class="locations-section">
          <h3 class="section-title">All Locations</h3>
          <div class="locations-list">
            <div
              class="location-item"
              v-for="location in locations"
              :key="location.id"
            >
              <div class="location-icon">📍</div>
              <div class="location-text">{{ location.address }}</div>
            </div>
          </div>
        </div>

        <!-- Official Websites Section -->
        <div class="websites-section">
          <h3 class="section-title">Official Websites</h3>
          <div class="websites-list">
            <div
              class="website-item"
              v-for="website in websites"
              :key="website.id"
            >
              <div class="website-info">
                <h4 class="website-name">{{ website.name }}</h4>
                <a href="#" class="website-link">Go to Site</a>
              </div>
              <div class="external-link-icon">🔗</div>
            </div>
          </div>
        </div>
      </aside>

      <!-- Center Content -->
      <section class="center-content">
        <!-- Our Restaurants Section -->
        <div class="restaurants-section">
          <h2 class="section-title">Our Restaurants</h2>
          <div class="restaurants-grid">
            <div
              class="restaurant-card"
              v-for="restaurant in restaurants"
              :key="restaurant.id"
              :class="{ featured: restaurant.featured }"
            >
              <div
                class="restaurant-image"
                :style="{ backgroundColor: restaurant.imageColor }"
              >
                <!-- Restaurant image placeholder -->
              </div>
              <div class="restaurant-info">
                <h3 class="restaurant-name">{{ restaurant.name }}</h3>
                <p class="restaurant-address">{{ restaurant.address }}</p>
                <p class="restaurant-hours">{{ restaurant.hours }}</p>
              </div>
              <!-- Time slots for featured restaurant -->
              <div v-if="restaurant.featured" class="time-slots">
                <div
                  class="time-slot"
                  v-for="time in restaurant.timeSlots"
                  :key="time"
                >
                  {{ time }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Right Content -->
      <aside class="right-sidebar">
        <!-- Background Image -->
        <div class="background-image"></div>

        <!-- Mobile App Section -->
        <div class="mobile-app-section">
          <div class="phone-mockup"></div>
          <div class="app-download">
            <h3 class="download-title">DOWNLOAD THE APP</h3>
            <div class="download-buttons">
              <button class="download-btn android">
                <div class="play-icon">▶</div>
                <span>Get it On Android</span>
              </button>
              <button class="download-btn ios">
                <div class="apple-icon">🍎</div>
                <span>Get it On iOS</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Profile Images -->
        <div class="profile-images">
          <div class="profile-img profile-1"></div>
          <div class="profile-img profile-2"></div>
          <div class="profile-img profile-3"></div>
        </div>
      </aside>
    </main>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-background"></div>
      <div class="footer-content">
        <h2 class="footer-title">RESERVE YOUR TABLE</h2>
        <p class="footer-subtitle">Experience fine dining at its best</p>
        <button class="reserve-btn">
          <span>Reserve Now</span>
          <div class="arrow-icon">→</div>
        </button>
      </div>
      <div class="footer-bottom">
        <div class="footer-info">
          <h3 class="contact-title">Contact Us</h3>
          <div class="social-links">
            <div class="social-icon">📘</div>
            <div class="social-icon">📷</div>
            <div class="social-icon">🐦</div>
          </div>
        </div>
        <p class="copyright">© 2024 Dine in Florida. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: "HomePage",
  data() {
    return {
      locations: [
        {
          id: 1,
          address: "3913 NE 163rd St North Miami Beach, FL 33160",
        },
        {
          id: 2,
          address: "1 American Dream Way. #F225East Rutherford, NJ 07073",
        },
        {
          id: 3,
          address: "1760 Sawgrass Mills CircleSunrise, FL 33323-3912",
        },
        {
          id: 4,
          address: "4250 Salzedo Street, Suite 1425Coral Gables, FL 33146",
        },
        {
          id: 5,
          address: "344 Plaza Real, Suite 1433Boca Raton, FL 33432-3937",
        },
        {
          id: 6,
          address:
            "360 San Lorenzo Avenue, Suite 1430Coral Gables, FL 33146-1865",
        },
      ],
      websites: [
        {
          id: 1,
          name: "Villagio Restaurants & Bar",
        },
        {
          id: 2,
          name: "Seagrill Miami Restaurants",
        },
        {
          id: 3,
          name: "Carpaccio American Dream",
        },
      ],
      restaurants: [
        {
          id: 1,
          name: "Sea Grill North Miami Beach",
          address: "3913 NE 163rd St North Miami Beach, FL 33160",
          hours: "11:30 AM - 11:00 PM",
          imageColor: "#ff4444",
          featured: false,
          timeSlots: [],
        },
        {
          id: 2,
          name: "Carpaccio American Dream",
          address: "1 American Dream Way. #F225East Rutherford, NJ 07073",
          hours: "11:30 AM - 11:00 PM",
          imageColor: "#ff4444",
          featured: false,
          timeSlots: [],
        },
        {
          id: 3,
          name: "Sea Grill of Merrick Park",
          address: "4250 Salzedo Street, Suite 1425Coral Gables, FL 33146",
          hours: "11:30 AM - 11:00 PM",
          imageColor: "#ff4444",
          featured: true,
          timeSlots: ["09:30", "10:15", "11:15"],
        },
        {
          id: 4,
          name: "Villagio Restaurant & Bar",
          address: "1760 Sawgrass Mills CircleSunrise, FL 33323-3912",
          hours: "11:30 AM - 11:00 PM",
          imageColor: "#ff4444",
          featured: false,
          timeSlots: ["09:30", "10:15", "11:15"],
        },
        {
          id: 5,
          name: "Villagio Restaurant & Bar",
          address: "344 Plaza Real, Suite 1433 Boca Raton, FL 33432-3937",
          hours: "11:30 AM - 11:00 PM",
          imageColor: "#ff4444",
          featured: false,
          timeSlots: ["09:30", "10:15", "11:15"],
        },
        {
          id: 6,
          name: "Villagio Restaurant & Bar",
          address:
            "360 San Lorenzo Avenue, Suite 1430Coral Gables, FL 33146-1865",
          hours: "11:30 AM - 11:00 PM",
          imageColor: "#ff4444",
          featured: false,
          timeSlots: ["09:30", "10:15", "11:15"],
        },
      ],
    };
  },
  methods: {
    reserveTable() {
      this.$message.success("Reservation feature coming soon!");
    },
    downloadApp(platform) {
      this.$message.info(`Download ${platform} app`);
    },
  },
};
</script>

<style scoped>
/* Main container */
.restaurant-home {
  width: 100%;
  min-height: 100vh;
  background: #f5f5f5;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.header {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1920px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
}

.logo {
  font-size: 30px;
  font-weight: 400;
  color: #2b2b2b;
  margin: 0;
}

.nav-menu {
  display: flex;
  gap: 30px;
  align-items: center;
}

.nav-item {
  font-size: 16px;
  font-weight: 600;
  color: #2b2b2b;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.nav-item.active {
  background: #f49b33;
  color: white;
}

.nav-item:hover {
  background: #f49b33;
  color: white;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.login-btn,
.signup-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn {
  background: transparent;
  color: #2b2b2b;
}

.signup-btn {
  background: #f49b33;
  color: white;
}

.profile-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #ff4444;
}

/* Main Content */
.main-content {
  max-width: 1920px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 327px 1fr 400px;
  gap: 20px;
  padding: 20px;
  min-height: calc(100vh - 110px);
}

/* Left Sidebar */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.promo-banner {
  background: linear-gradient(135deg, #faff00 0%, #ed994c 100%);
  border-radius: 10px;
  padding: 20px;
}

.promo-text {
  font-size: 18px;
  color: #2b2b2b;
  margin: 0;
  line-height: 1.4;
}

.locations-section,
.websites-section {
  background: white;
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #5e5e5e;
  margin: 0 0 20px 0;
}

.locations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.location-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.location-icon {
  font-size: 16px;
  margin-top: 2px;
}

.location-text {
  font-size: 14px;
  color: #5e5e5e;
  line-height: 1.4;
}

.websites-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.website-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.website-name {
  font-size: 14px;
  font-weight: 700;
  color: #2b2b2b;
  margin: 0 0 5px 0;
}

.website-link {
  font-size: 10px;
  color: #f49b33;
  text-decoration: none;
}

.external-link-icon {
  font-size: 16px;
  color: #5e5e5e;
}

/* Center Content */
.center-content {
  background: white;
  border-radius: 5px;
  padding: 30px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
}

.restaurants-section .section-title {
  font-size: 24px;
  font-weight: 700;
  color: #5e5e5e;
  margin: 0 0 30px 0;
}

.restaurants-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.restaurant-card {
  background: white;
  border-radius: 5px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.restaurant-card.featured {
  background: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
}

.restaurant-image {
  width: 100%;
  height: 224px;
  background: #ff4444;
  border-radius: 5px;
  margin-bottom: 15px;
}

.restaurant-info {
  padding: 0 15px 15px;
}

.restaurant-name {
  font-size: 18px;
  font-weight: 700;
  color: #5e5e5e;
  margin: 0 0 10px 0;
}

.restaurant-address {
  font-size: 14px;
  color: #5e5e5e;
  margin: 0 0 10px 0;
  line-height: 1.4;
}

.restaurant-hours {
  font-size: 14px;
  color: #5e5e5e;
  margin: 0;
  text-align: center;
}

.time-slots {
  display: flex;
  gap: 20px;
  padding: 15px;
  justify-content: center;
}

.time-slot {
  background: #f49b33;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 700;
  text-align: center;
}

/* Right Sidebar */
.right-sidebar {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 346px;
  background: #ff4444;
  border-radius: 5px;
  z-index: 1;
}

.mobile-app-section {
  position: relative;
  z-index: 2;
  margin-top: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.phone-mockup {
  width: 200px;
  height: 400px;
  background: #ff4444;
  border-radius: 20px;
  margin-bottom: 20px;
}

.app-download {
  text-align: center;
}

.download-title {
  font-size: 32px;
  font-weight: 700;
  color: #5e5e5e;
  margin: 0 0 20px 0;
  text-transform: uppercase;
}

.download-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.download-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 20px;
  background: white;
  border: none;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.download-btn:hover {
  transform: translateY(-2px);
}

.play-icon,
.apple-icon {
  font-size: 22px;
}

.download-btn span {
  font-size: 16px;
  font-weight: 700;
  color: #2b2b2b;
}

.profile-images {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 50px;
}

.profile-img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #ff4444;
}

.profile-1 {
  background: #ff4444;
}

.profile-2 {
  background: #660b12;
}

.profile-3 {
  background: #000000;
}

/* Footer */
.footer {
  position: relative;
  width: 100%;
  height: 570px;
  margin-top: 50px;
}

.footer-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 300px;
  background: #ff4444;
}

.footer-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: rgba(43, 43, 43, 0.7);
  backdrop-filter: blur(8px);
  gap: 30px;
}

.footer-title {
  font-size: 30px;
  font-weight: 700;
  color: white;
  margin: 0;
  text-transform: uppercase;
}

.footer-subtitle {
  font-size: 18px;
  color: white;
  margin: 0;
  text-align: center;
}

.reserve-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 40px;
  background: #f49b33;
  border: none;
  border-radius: 5px;
  color: white;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reserve-btn:hover {
  background: #e8890a;
  transform: translateY(-2px);
}

.arrow-icon {
  font-size: 16px;
}

.footer-bottom {
  background: #2b2b2b;
  height: 270px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
}

.footer-info {
  text-align: center;
}

.contact-title {
  font-size: 32px;
  color: white;
  margin: 0 0 20px 0;
}

.social-links {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f49b33;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.social-icon:hover {
  transform: scale(1.1);
}

.copyright {
  font-size: 12px;
  color: #92989f;
  margin: 0;
  text-align: center;
  letter-spacing: 2px;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .main-content {
    grid-template-columns: 300px 1fr 350px;
    max-width: 1200px;
  }
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .restaurants-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .right-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    padding: 15px 20px;
  }

  .nav-menu {
    gap: 15px;
  }

  .main-content {
    padding: 15px;
  }

  .restaurants-grid {
    grid-template-columns: 1fr;
  }

  .download-buttons {
    flex-direction: row;
    gap: 15px;
  }
}
</style>
