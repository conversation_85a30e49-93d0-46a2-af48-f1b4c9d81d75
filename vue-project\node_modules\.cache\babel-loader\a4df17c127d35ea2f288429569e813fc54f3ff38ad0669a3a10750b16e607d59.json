{"ast": null, "code": "import Vue from \"vue\";\nimport App from \"./App.vue\";\nimport router from \"./router\";\nimport store from \"./store\";\nimport ElementUI from \"element-ui\";\nimport \"element-ui/lib/theme-chalk/index.css\";\nVue.config.productionTip = false;\nVue.use(ElementUI);\nnew Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount(\"#app\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "ElementUI", "config", "productionTip", "use", "render", "h", "$mount"], "sources": ["C:/work/testProduct/figma/vue-project/src/main.js"], "sourcesContent": ["import Vue from \"vue\";\nimport App from \"./App.vue\";\nimport router from \"./router\";\nimport store from \"./store\";\nimport ElementUI from \"element-ui\";\nimport \"element-ui/lib/theme-chalk/index.css\";\n\nVue.config.productionTip = false;\n\nVue.use(ElementUI);\n\nnew Vue({\n  router,\n  store,\n  render: (h) => h(App),\n}).$mount(\"#app\");\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAE7CJ,GAAG,CAACK,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCN,GAAG,CAACO,GAAG,CAACH,SAAS,CAAC;AAElB,IAAIJ,GAAG,CAAC;EACNE,MAAM;EACNC,KAAK;EACLK,MAAM,EAAGC,CAAC,IAAKA,CAAC,CAACR,GAAG;AACtB,CAAC,CAAC,CAACS,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}