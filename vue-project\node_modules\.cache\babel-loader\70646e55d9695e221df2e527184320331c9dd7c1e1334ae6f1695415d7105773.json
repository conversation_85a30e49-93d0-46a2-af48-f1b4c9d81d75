{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"home\"\n  }, [_c(\"el-container\", [_c(\"el-header\", [_c(\"h1\", [_vm._v(\"Vue2 项目首页\")])]), _c(\"el-main\", [_c(\"el-row\", {\n    attrs: {\n      gutter: 20\n    }\n  }, [_c(\"el-col\", {\n    attrs: {\n      span: 12\n    }\n  }, [_c(\"el-card\", {\n    staticClass: \"box-card\"\n  }, [_c(\"div\", {\n    staticClass: \"clearfix\",\n    attrs: {\n      slot: \"header\"\n    },\n    slot: \"header\"\n  }, [_c(\"span\", [_vm._v(\"Vuex 状态管理示例\")])]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_c(\"p\", [_vm._v(\"当前计数: \" + _vm._s(_vm.count))]), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: _vm.increment\n    }\n  }, [_vm._v(\"增加\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"danger\"\n    },\n    on: {\n      click: _vm.decrement\n    }\n  }, [_vm._v(\"减少\")])], 1)])], 1), _c(\"el-col\", {\n    attrs: {\n      span: 12\n    }\n  }, [_c(\"el-card\", {\n    staticClass: \"box-card\"\n  }, [_c(\"div\", {\n    staticClass: \"clearfix\",\n    attrs: {\n      slot: \"header\"\n    },\n    slot: \"header\"\n  }, [_c(\"span\", [_vm._v(\"Axios 请求示例\")])]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"success\"\n    },\n    on: {\n      click: _vm.fetchData\n    }\n  }, [_vm._v(\"获取数据\")]), _vm.apiData ? _c(\"p\", [_vm._v(_vm._s(_vm.apiData))]) : _vm._e()], 1)])], 1)], 1)], 1)], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "attrs", "gutter", "span", "slot", "_s", "count", "type", "on", "click", "increment", "decrement", "fetchData", "apiData", "_e", "staticRenderFns", "_withStripped"], "sources": ["C:/work/testProduct/figma/vue-project/src/views/Home.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"home\" },\n    [\n      _c(\n        \"el-container\",\n        [\n          _c(\"el-header\", [_c(\"h1\", [_vm._v(\"Vue2 项目首页\")])]),\n          _c(\n            \"el-main\",\n            [\n              _c(\n                \"el-row\",\n                { attrs: { gutter: 20 } },\n                [\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\"el-card\", { staticClass: \"box-card\" }, [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"clearfix\",\n                            attrs: { slot: \"header\" },\n                            slot: \"header\",\n                          },\n                          [_c(\"span\", [_vm._v(\"Vuex 状态管理示例\")])]\n                        ),\n                        _c(\n                          \"div\",\n                          { staticClass: \"text item\" },\n                          [\n                            _c(\"p\", [_vm._v(\"当前计数: \" + _vm._s(_vm.count))]),\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"primary\" },\n                                on: { click: _vm.increment },\n                              },\n                              [_vm._v(\"增加\")]\n                            ),\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"danger\" },\n                                on: { click: _vm.decrement },\n                              },\n                              [_vm._v(\"减少\")]\n                            ),\n                          ],\n                          1\n                        ),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 12 } },\n                    [\n                      _c(\"el-card\", { staticClass: \"box-card\" }, [\n                        _c(\n                          \"div\",\n                          {\n                            staticClass: \"clearfix\",\n                            attrs: { slot: \"header\" },\n                            slot: \"header\",\n                          },\n                          [_c(\"span\", [_vm._v(\"Axios 请求示例\")])]\n                        ),\n                        _c(\n                          \"div\",\n                          { staticClass: \"text item\" },\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: { type: \"success\" },\n                                on: { click: _vm.fetchData },\n                              },\n                              [_vm._v(\"获取数据\")]\n                            ),\n                            _vm.apiData\n                              ? _c(\"p\", [_vm._v(_vm._s(_vm.apiData))])\n                              : _vm._e(),\n                          ],\n                          1\n                        ),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAO,CAAC,EACvB,CACEF,EAAE,CACA,cAAc,EACd,CACEA,EAAE,CAAC,WAAW,EAAE,CAACA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAClDH,EAAE,CACA,SAAS,EACT,CACEA,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAG;EAAE,CAAC,EACzB,CACEL,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAA<PERSON>,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACEN,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,UAAU;IACvBE,KAAK,EAAE;MAAEG,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CAACP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CACtC,CAAC,EACDH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,GAAGJ,GAAG,CAACS,EAAE,CAACT,GAAG,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAC/CT,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEM,IAAI,EAAE;IAAU,CAAC;IAC1BC,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACc;IAAU;EAC7B,CAAC,EACD,CAACd,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDH,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEM,IAAI,EAAE;IAAS,CAAC;IACzBC,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACe;IAAU;EAC7B,CAAC,EACD,CAACf,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IAAEI,KAAK,EAAE;MAAEE,IAAI,EAAE;IAAG;EAAE,CAAC,EACvB,CACEN,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,UAAU;IACvBE,KAAK,EAAE;MAAEG,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CAACP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CACrC,CAAC,EACDH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MAAEM,IAAI,EAAE;IAAU,CAAC;IAC1BC,EAAE,EAAE;MAAEC,KAAK,EAAEb,GAAG,CAACgB;IAAU;EAC7B,CAAC,EACD,CAAChB,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDJ,GAAG,CAACiB,OAAO,GACPhB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACS,EAAE,CAACT,GAAG,CAACiB,OAAO,CAAC,CAAC,CAAC,CAAC,GACtCjB,GAAG,CAACkB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBpB,MAAM,CAACqB,aAAa,GAAG,IAAI;AAE3B,SAASrB,MAAM,EAAEoB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}