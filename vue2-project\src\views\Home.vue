<template>
  <div class="home">
    <el-container>
      <el-header>
        <h1>Vue2 项目首页</h1>
      </el-header>
      <el-main>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>Vuex 状态管理示例</span>
              </div>
              <div class="text item">
                <p>当前计数: {{ count }}</p>
                <el-button @click="increment" type="primary">增加</el-button>
                <el-button @click="decrement" type="danger">减少</el-button>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>Axios 请求示例</span>
              </div>
              <div class="text item">
                <el-button @click="fetchData" type="success">获取数据</el-button>
                <p v-if="apiData">{{ apiData }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import axios from 'axios'

export default {
  name: 'Home',
  data() {
    return {
      apiData: null
    }
  },
  computed: {
    ...mapState(['count'])
  },
  methods: {
    ...mapActions(['increment', 'decrement']),
    async fetchData() {
      try {
        const response = await axios.get('https://jsonplaceholder.typicode.com/posts/1')
        this.apiData = response.data.title
        this.$message.success('数据获取成功!')
      } catch (error) {
        this.$message.error('数据获取失败!')
        console.error(error)
      }
    }
  }
}
</script>

<style scoped>
.home {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
</style>
