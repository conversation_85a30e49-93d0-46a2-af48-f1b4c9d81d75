{"name": "pseudomap", "version": "1.0.2", "description": "A thing that is a lot like ES6 `Map`, but without iterators, for use in environments where `for..of` syntax and `Map` are not available.", "main": "map.js", "directories": {"test": "test"}, "devDependencies": {"tap": "^2.3.1"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/pseudomap.git"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "bugs": {"url": "https://github.com/isaacs/pseudomap/issues"}, "homepage": "https://github.com/isaacs/pseudomap#readme"}