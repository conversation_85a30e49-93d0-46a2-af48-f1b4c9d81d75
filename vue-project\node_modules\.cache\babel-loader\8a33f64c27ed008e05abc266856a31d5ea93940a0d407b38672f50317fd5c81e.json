{"ast": null, "code": "export default {\n  name: \"<PERSON>P<PERSON>\",\n  data() {\n    return {\n      locations: [{\n        id: 1,\n        address: \"3913 NE 163rd St North Miami Beach, FL 33160\"\n      }, {\n        id: 2,\n        address: \"1 American Dream Way. #F225East Rutherford, NJ 07073\"\n      }, {\n        id: 3,\n        address: \"1760 Sawgrass Mills CircleSunrise, FL 33323-3912\"\n      }, {\n        id: 4,\n        address: \"4250 Salzedo Street, Suite 1425Coral Gables, FL 33146\"\n      }, {\n        id: 5,\n        address: \"344 Plaza Real, Suite 1433Boca Raton, FL 33432-3937\"\n      }, {\n        id: 6,\n        address: \"360 San Lorenzo Avenue, Suite 1430Coral Gables, FL 33146-1865\"\n      }],\n      websites: [{\n        id: 1,\n        name: \"Villagio Restaurants & Bar\"\n      }, {\n        id: 2,\n        name: \"Seagrill Miami Restaurants\"\n      }, {\n        id: 3,\n        name: \"Carpaccio American Dream\"\n      }],\n      restaurants: [{\n        id: 1,\n        name: \"Sea Grill North Miami Beach\",\n        address: \"3913 NE 163rd St North Miami Beach, FL 33160\",\n        hours: \"11:30 AM - 11:00 PM\",\n        imageColor: \"#ff4444\",\n        featured: false,\n        timeSlots: []\n      }, {\n        id: 2,\n        name: \"Carpaccio American Dream\",\n        address: \"1 American Dream Way. #F225East Rutherford, NJ 07073\",\n        hours: \"11:30 AM - 11:00 PM\",\n        imageColor: \"#ff4444\",\n        featured: false,\n        timeSlots: []\n      }, {\n        id: 3,\n        name: \"Sea Grill of Merrick Park\",\n        address: \"4250 Salzedo Street, Suite 1425Coral Gables, FL 33146\",\n        hours: \"11:30 AM - 11:00 PM\",\n        imageColor: \"#ff4444\",\n        featured: true,\n        timeSlots: [\"09:30\", \"10:15\", \"11:15\"]\n      }, {\n        id: 4,\n        name: \"Villagio Restaurant & Bar\",\n        address: \"1760 Sawgrass Mills CircleSunrise, FL 33323-3912\",\n        hours: \"11:30 AM - 11:00 PM\",\n        imageColor: \"#ff4444\",\n        featured: false,\n        timeSlots: [\"09:30\", \"10:15\", \"11:15\"]\n      }, {\n        id: 5,\n        name: \"Villagio Restaurant & Bar\",\n        address: \"344 Plaza Real, Suite 1433 Boca Raton, FL 33432-3937\",\n        hours: \"11:30 AM - 11:00 PM\",\n        imageColor: \"#ff4444\",\n        featured: false,\n        timeSlots: [\"09:30\", \"10:15\", \"11:15\"]\n      }, {\n        id: 6,\n        name: \"Villagio Restaurant & Bar\",\n        address: \"360 San Lorenzo Avenue, Suite 1430Coral Gables, FL 33146-1865\",\n        hours: \"11:30 AM - 11:00 PM\",\n        imageColor: \"#ff4444\",\n        featured: false,\n        timeSlots: [\"09:30\", \"10:15\", \"11:15\"]\n      }]\n    };\n  },\n  methods: {\n    reserveTable() {\n      this.$message.success(\"Reservation feature coming soon!\");\n    },\n    downloadApp(platform) {\n      this.$message.info(`Download ${platform} app`);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "locations", "id", "address", "websites", "restaurants", "hours", "imageColor", "featured", "timeSlots", "methods", "reserveTable", "$message", "success", "downloadApp", "platform", "info"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\n  <div class=\"restaurant-home\">\n    <!-- Header -->\n    <header class=\"header\">\n      <div class=\"header-content\">\n        <h1 class=\"logo\">DINE IN FLORIDA</h1>\n        <nav class=\"nav-menu\">\n          <a href=\"#\" class=\"nav-item active\">Home</a>\n          <a href=\"#\" class=\"nav-item\">Restaurants</a>\n          <a href=\"#\" class=\"nav-item\">Reservations</a>\n        </nav>\n        <div class=\"header-actions\">\n          <button class=\"login-btn\">Login</button>\n          <button class=\"signup-btn\">Sign Up</button>\n          <div class=\"profile-avatar\"></div>\n        </div>\n      </div>\n    </header>\n\n    <!-- Main Content -->\n    <main class=\"main-content\">\n      <!-- Left Sidebar -->\n      <aside class=\"sidebar\">\n        <!-- Promotional Banner -->\n        <div class=\"promo-banner\">\n          <p class=\"promo-text\">\n            Automatically save 2% on your bill if you reserve your Table With\n            DINE IN FLORIDA\n          </p>\n        </div>\n\n        <!-- All Locations Section -->\n        <div class=\"locations-section\">\n          <h3 class=\"section-title\">All Locations</h3>\n          <div class=\"locations-list\">\n            <div\n              class=\"location-item\"\n              v-for=\"location in locations\"\n              :key=\"location.id\"\n            >\n              <div class=\"location-icon\">📍</div>\n              <div class=\"location-text\">{{ location.address }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Official Websites Section -->\n        <div class=\"websites-section\">\n          <h3 class=\"section-title\">Official Websites</h3>\n          <div class=\"websites-list\">\n            <div\n              class=\"website-item\"\n              v-for=\"website in websites\"\n              :key=\"website.id\"\n            >\n              <div class=\"website-info\">\n                <h4 class=\"website-name\">{{ website.name }}</h4>\n                <a href=\"#\" class=\"website-link\">Go to Site</a>\n              </div>\n              <div class=\"external-link-icon\">🔗</div>\n            </div>\n          </div>\n        </div>\n      </aside>\n\n      <!-- Center Content -->\n      <section class=\"center-content\">\n        <!-- Our Restaurants Section -->\n        <div class=\"restaurants-section\">\n          <h2 class=\"section-title\">Our Restaurants</h2>\n          <div class=\"restaurants-grid\">\n            <div\n              class=\"restaurant-card\"\n              v-for=\"restaurant in restaurants\"\n              :key=\"restaurant.id\"\n              :class=\"{ featured: restaurant.featured }\"\n            >\n              <div\n                class=\"restaurant-image\"\n                :style=\"{ backgroundColor: restaurant.imageColor }\"\n              >\n                <!-- Restaurant image placeholder -->\n              </div>\n              <div class=\"restaurant-info\">\n                <h3 class=\"restaurant-name\">{{ restaurant.name }}</h3>\n                <p class=\"restaurant-address\">{{ restaurant.address }}</p>\n                <p class=\"restaurant-hours\">{{ restaurant.hours }}</p>\n              </div>\n              <!-- Time slots for featured restaurant -->\n              <div v-if=\"restaurant.featured\" class=\"time-slots\">\n                <div\n                  class=\"time-slot\"\n                  v-for=\"time in restaurant.timeSlots\"\n                  :key=\"time\"\n                >\n                  {{ time }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Right Content -->\n      <aside class=\"right-sidebar\">\n        <!-- Background Image -->\n        <div class=\"background-image\"></div>\n\n        <!-- Mobile App Section -->\n        <div class=\"mobile-app-section\">\n          <div class=\"phone-mockup\"></div>\n          <div class=\"app-download\">\n            <h3 class=\"download-title\">DOWNLOAD THE APP</h3>\n            <div class=\"download-buttons\">\n              <button class=\"download-btn android\">\n                <div class=\"play-icon\">▶</div>\n                <span>Get it On Android</span>\n              </button>\n              <button class=\"download-btn ios\">\n                <div class=\"apple-icon\">🍎</div>\n                <span>Get it On iOS</span>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Profile Images -->\n        <div class=\"profile-images\">\n          <div class=\"profile-img profile-1\"></div>\n          <div class=\"profile-img profile-2\"></div>\n          <div class=\"profile-img profile-3\"></div>\n        </div>\n      </aside>\n    </main>\n\n    <!-- Footer -->\n    <footer class=\"footer\">\n      <div class=\"footer-background\"></div>\n      <div class=\"footer-content\">\n        <h2 class=\"footer-title\">RESERVE YOUR TABLE</h2>\n        <p class=\"footer-subtitle\">Experience fine dining at its best</p>\n        <button class=\"reserve-btn\">\n          <span>Reserve Now</span>\n          <div class=\"arrow-icon\">→</div>\n        </button>\n      </div>\n      <div class=\"footer-bottom\">\n        <div class=\"footer-info\">\n          <h3 class=\"contact-title\">Contact Us</h3>\n          <div class=\"social-links\">\n            <div class=\"social-icon\">📘</div>\n            <div class=\"social-icon\">📷</div>\n            <div class=\"social-icon\">🐦</div>\n          </div>\n        </div>\n        <p class=\"copyright\">© 2024 Dine in Florida. All rights reserved.</p>\n      </div>\n    </footer>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"HomePage\",\n  data() {\n    return {\n      locations: [\n        {\n          id: 1,\n          address: \"3913 NE 163rd St North Miami Beach, FL 33160\",\n        },\n        {\n          id: 2,\n          address: \"1 American Dream Way. #F225East Rutherford, NJ 07073\",\n        },\n        {\n          id: 3,\n          address: \"1760 Sawgrass Mills CircleSunrise, FL 33323-3912\",\n        },\n        {\n          id: 4,\n          address: \"4250 Salzedo Street, Suite 1425Coral Gables, FL 33146\",\n        },\n        {\n          id: 5,\n          address: \"344 Plaza Real, Suite 1433Boca Raton, FL 33432-3937\",\n        },\n        {\n          id: 6,\n          address:\n            \"360 San Lorenzo Avenue, Suite 1430Coral Gables, FL 33146-1865\",\n        },\n      ],\n      websites: [\n        {\n          id: 1,\n          name: \"Villagio Restaurants & Bar\",\n        },\n        {\n          id: 2,\n          name: \"Seagrill Miami Restaurants\",\n        },\n        {\n          id: 3,\n          name: \"Carpaccio American Dream\",\n        },\n      ],\n      restaurants: [\n        {\n          id: 1,\n          name: \"Sea Grill North Miami Beach\",\n          address: \"3913 NE 163rd St North Miami Beach, FL 33160\",\n          hours: \"11:30 AM - 11:00 PM\",\n          imageColor: \"#ff4444\",\n          featured: false,\n          timeSlots: [],\n        },\n        {\n          id: 2,\n          name: \"Carpaccio American Dream\",\n          address: \"1 American Dream Way. #F225East Rutherford, NJ 07073\",\n          hours: \"11:30 AM - 11:00 PM\",\n          imageColor: \"#ff4444\",\n          featured: false,\n          timeSlots: [],\n        },\n        {\n          id: 3,\n          name: \"Sea Grill of Merrick Park\",\n          address: \"4250 Salzedo Street, Suite 1425Coral Gables, FL 33146\",\n          hours: \"11:30 AM - 11:00 PM\",\n          imageColor: \"#ff4444\",\n          featured: true,\n          timeSlots: [\"09:30\", \"10:15\", \"11:15\"],\n        },\n        {\n          id: 4,\n          name: \"Villagio Restaurant & Bar\",\n          address: \"1760 Sawgrass Mills CircleSunrise, FL 33323-3912\",\n          hours: \"11:30 AM - 11:00 PM\",\n          imageColor: \"#ff4444\",\n          featured: false,\n          timeSlots: [\"09:30\", \"10:15\", \"11:15\"],\n        },\n        {\n          id: 5,\n          name: \"Villagio Restaurant & Bar\",\n          address: \"344 Plaza Real, Suite 1433 Boca Raton, FL 33432-3937\",\n          hours: \"11:30 AM - 11:00 PM\",\n          imageColor: \"#ff4444\",\n          featured: false,\n          timeSlots: [\"09:30\", \"10:15\", \"11:15\"],\n        },\n        {\n          id: 6,\n          name: \"Villagio Restaurant & Bar\",\n          address:\n            \"360 San Lorenzo Avenue, Suite 1430Coral Gables, FL 33146-1865\",\n          hours: \"11:30 AM - 11:00 PM\",\n          imageColor: \"#ff4444\",\n          featured: false,\n          timeSlots: [\"09:30\", \"10:15\", \"11:15\"],\n        },\n      ],\n    };\n  },\n  methods: {\n    reserveTable() {\n      this.$message.success(\"Reservation feature coming soon!\");\n    },\n    downloadApp(platform) {\n      this.$message.info(`Download ${platform} app`);\n    },\n  },\n};\n</script>\n\n<style scoped>\n/* Main container */\n.restaurant-home {\n  width: 100%;\n  min-height: 100vh;\n  background: #f5f5f5;\n  font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif;\n}\n\n/* Header */\n.header {\n  background: #ffffff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.header-content {\n  max-width: 1920px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 40px;\n}\n\n.logo {\n  font-size: 30px;\n  font-weight: 400;\n  color: #2b2b2b;\n  margin: 0;\n}\n\n.nav-menu {\n  display: flex;\n  gap: 30px;\n  align-items: center;\n}\n\n.nav-item {\n  font-size: 16px;\n  font-weight: 600;\n  color: #2b2b2b;\n  text-decoration: none;\n  padding: 10px 20px;\n  border-radius: 5px;\n  transition: all 0.3s ease;\n}\n\n.nav-item.active {\n  background: #f49b33;\n  color: white;\n}\n\n.nav-item:hover {\n  background: #f49b33;\n  color: white;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.login-btn,\n.signup-btn {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 5px;\n  font-size: 16px;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.login-btn {\n  background: transparent;\n  color: #2b2b2b;\n}\n\n.signup-btn {\n  background: #f49b33;\n  color: white;\n}\n\n.profile-avatar {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  background: #ff4444;\n}\n\n/* Main Content */\n.main-content {\n  max-width: 1920px;\n  margin: 0 auto;\n  display: grid;\n  grid-template-columns: 327px 1fr 400px;\n  gap: 20px;\n  padding: 20px;\n  min-height: calc(100vh - 110px);\n}\n\n/* Left Sidebar */\n.sidebar {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.promo-banner {\n  background: linear-gradient(135deg, #faff00 0%, #ed994c 100%);\n  border-radius: 10px;\n  padding: 20px;\n}\n\n.promo-text {\n  font-size: 18px;\n  color: #2b2b2b;\n  margin: 0;\n  line-height: 1.4;\n}\n\n.locations-section,\n.websites-section {\n  background: white;\n  border-radius: 5px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n}\n\n.section-title {\n  font-size: 20px;\n  font-weight: 700;\n  color: #5e5e5e;\n  margin: 0 0 20px 0;\n}\n\n.locations-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.location-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 10px;\n}\n\n.location-icon {\n  font-size: 16px;\n  margin-top: 2px;\n}\n\n.location-text {\n  font-size: 14px;\n  color: #5e5e5e;\n  line-height: 1.4;\n}\n\n.websites-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.website-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.website-name {\n  font-size: 14px;\n  font-weight: 700;\n  color: #2b2b2b;\n  margin: 0 0 5px 0;\n}\n\n.website-link {\n  font-size: 10px;\n  color: #f49b33;\n  text-decoration: none;\n}\n\n.external-link-icon {\n  font-size: 16px;\n  color: #5e5e5e;\n}\n\n/* Center Content */\n.center-content {\n  background: white;\n  border-radius: 5px;\n  padding: 30px;\n  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);\n}\n\n.restaurants-section .section-title {\n  font-size: 24px;\n  font-weight: 700;\n  color: #5e5e5e;\n  margin: 0 0 30px 0;\n}\n\n.restaurants-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 30px;\n}\n\n.restaurant-card {\n  background: white;\n  border-radius: 5px;\n  overflow: hidden;\n  transition: transform 0.3s ease;\n}\n\n.restaurant-card.featured {\n  background: white;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n  transform: scale(1.02);\n}\n\n.restaurant-image {\n  width: 100%;\n  height: 224px;\n  background: #ff4444;\n  border-radius: 5px;\n  margin-bottom: 15px;\n}\n\n.restaurant-info {\n  padding: 0 15px 15px;\n}\n\n.restaurant-name {\n  font-size: 18px;\n  font-weight: 700;\n  color: #5e5e5e;\n  margin: 0 0 10px 0;\n}\n\n.restaurant-address {\n  font-size: 14px;\n  color: #5e5e5e;\n  margin: 0 0 10px 0;\n  line-height: 1.4;\n}\n\n.restaurant-hours {\n  font-size: 14px;\n  color: #5e5e5e;\n  margin: 0;\n  text-align: center;\n}\n\n.time-slots {\n  display: flex;\n  gap: 20px;\n  padding: 15px;\n  justify-content: center;\n}\n\n.time-slot {\n  background: #f49b33;\n  color: white;\n  padding: 10px 20px;\n  border-radius: 5px;\n  font-size: 16px;\n  font-weight: 700;\n  text-align: center;\n}\n\n/* Right Sidebar */\n.right-sidebar {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.background-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 346px;\n  background: #ff4444;\n  border-radius: 5px;\n  z-index: 1;\n}\n\n.mobile-app-section {\n  position: relative;\n  z-index: 2;\n  margin-top: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20px;\n}\n\n.phone-mockup {\n  width: 200px;\n  height: 400px;\n  background: #ff4444;\n  border-radius: 20px;\n  margin-bottom: 20px;\n}\n\n.app-download {\n  text-align: center;\n}\n\n.download-title {\n  font-size: 32px;\n  font-weight: 700;\n  color: #5e5e5e;\n  margin: 0 0 20px 0;\n  text-transform: uppercase;\n}\n\n.download-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.download-btn {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 6px 20px;\n  background: white;\n  border: none;\n  border-radius: 5px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);\n  cursor: pointer;\n  transition: transform 0.3s ease;\n}\n\n.download-btn:hover {\n  transform: translateY(-2px);\n}\n\n.play-icon,\n.apple-icon {\n  font-size: 22px;\n}\n\n.download-btn span {\n  font-size: 16px;\n  font-weight: 700;\n  color: #2b2b2b;\n}\n\n.profile-images {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  margin-top: 50px;\n}\n\n.profile-img {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  background: #ff4444;\n}\n\n.profile-1 {\n  background: #ff4444;\n}\n\n.profile-2 {\n  background: #660b12;\n}\n\n.profile-3 {\n  background: #000000;\n}\n\n/* Footer */\n.footer {\n  position: relative;\n  width: 100%;\n  height: 570px;\n  margin-top: 50px;\n}\n\n.footer-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 300px;\n  background: #ff4444;\n}\n\n.footer-content {\n  position: relative;\n  z-index: 2;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 300px;\n  background: rgba(43, 43, 43, 0.7);\n  backdrop-filter: blur(8px);\n  gap: 30px;\n}\n\n.footer-title {\n  font-size: 30px;\n  font-weight: 700;\n  color: white;\n  margin: 0;\n  text-transform: uppercase;\n}\n\n.footer-subtitle {\n  font-size: 18px;\n  color: white;\n  margin: 0;\n  text-align: center;\n}\n\n.reserve-btn {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 10px 40px;\n  background: #f49b33;\n  border: none;\n  border-radius: 5px;\n  color: white;\n  font-size: 16px;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.reserve-btn:hover {\n  background: #e8890a;\n  transform: translateY(-2px);\n}\n\n.arrow-icon {\n  font-size: 16px;\n}\n\n.footer-bottom {\n  background: #2b2b2b;\n  height: 270px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 30px;\n}\n\n.footer-info {\n  text-align: center;\n}\n\n.contact-title {\n  font-size: 32px;\n  color: white;\n  margin: 0 0 20px 0;\n}\n\n.social-links {\n  display: flex;\n  gap: 20px;\n  justify-content: center;\n}\n\n.social-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: #f49b33;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n}\n\n.social-icon:hover {\n  transform: scale(1.1);\n}\n\n.copyright {\n  font-size: 12px;\n  color: #92989f;\n  margin: 0;\n  text-align: center;\n  letter-spacing: 2px;\n}\n\n/* Responsive Design */\n@media (max-width: 1400px) {\n  .main-content {\n    grid-template-columns: 300px 1fr 350px;\n    max-width: 1200px;\n  }\n}\n\n@media (max-width: 1024px) {\n  .main-content {\n    grid-template-columns: 1fr;\n    gap: 30px;\n  }\n\n  .restaurants-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .right-sidebar {\n    order: -1;\n  }\n}\n\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    gap: 20px;\n    padding: 15px 20px;\n  }\n\n  .nav-menu {\n    gap: 15px;\n  }\n\n  .main-content {\n    padding: 15px;\n  }\n\n  .restaurants-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .download-buttons {\n    flex-direction: row;\n    gap: 15px;\n  }\n}\n</style>\n\n/* Features Section */\n.features-section {\n  position: relative;\n  margin-top: 100px;\n  padding: 0 80px;\n}\n\n.feature-card {\n  position: absolute;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(40px);\n  padding: 40px;\n  color: #ffffff;\n}\n\n.feature-left {\n  left: 80px;\n  bottom: -200px;\n  width: 389px;\n  height: 489px;\n  border-radius: 0 89px 0 0;\n}\n\n.feature-right {\n  right: 80px;\n  bottom: -200px;\n  width: 560px;\n  height: 292px;\n  border-radius: 0 89px 0 0;\n}\n\n.feature-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.feature-title {\n  font-family: \"Inter\", sans-serif;\n  font-weight: 600;\n  font-size: 24px;\n  line-height: 1.5;\n  letter-spacing: 4%;\n  text-transform: uppercase;\n  color: #ffffff;\n  margin: 0 0 20px 0;\n}\n\n.feature-description {\n  font-family: \"Inter\", sans-serif;\n  font-size: 20px;\n  line-height: 1.5;\n  letter-spacing: 4%;\n  color: rgba(255, 255, 255, 0.8);\n  margin: 0 0 40px 0;\n  flex-grow: 1;\n}\n\n.discover-more {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.discover-more:hover {\n  transform: translateX(5px);\n}\n\n.discover-text {\n  font-family: \"Inter\", sans-serif;\n  font-size: 20px;\n  line-height: 1.5;\n  letter-spacing: 4%;\n  color: #1df659;\n}\n\n.discover-button {\n  width: 45px;\n  height: 45px;\n  background: rgba(29, 246, 89, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.discover-button::before {\n  content: \"\";\n  position: absolute;\n  width: 37px;\n  height: 37px;\n  background: #1df659;\n  border-radius: 50%;\n  top: 4px;\n  left: 4px;\n}\n\n.arrow-icon {\n  position: relative;\n  z-index: 1;\n  color: #080808;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n/* Decorative blur elements */\n.decorative-blur {\n  position: absolute;\n  filter: blur(500px);\n  pointer-events: none;\n}\n\n.green-blur {\n  width: 215px;\n  height: 415px;\n  background: #087e69;\n  left: 0;\n  bottom: 0;\n}\n\n.red-blur {\n  width: 215px;\n  height: 415px;\n  background: #d30202;\n  right: 0;\n  bottom: 200px;\n}\n\n/* Footer */\n.footer {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 351px;\n  height: 63px;\n  background: rgba(0, 0, 0, 0.11);\n  backdrop-filter: blur(100px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\n.footer-text {\n  font-family: \"Poppins\", sans-serif;\n  font-weight: 600;\n  font-size: 24px;\n  line-height: 1.6;\n  letter-spacing: 10%;\n  color: #000000;\n}\n\n/* Responsive adjustments */\n@media (max-width: 1600px) {\n  .save-earth-home {\n    width: 100%;\n    height: 100vh;\n  }\n\n  .main-container {\n    width: 90%;\n    max-width: 1440px;\n    height: auto;\n    min-height: 80vh;\n  }\n\n  .background-text {\n    font-size: 400px;\n  }\n\n  .hero-title {\n    font-size: 150px;\n  }\n\n  .feature-left,\n  .feature-right {\n    position: relative;\n    bottom: auto;\n    left: auto;\n    right: auto;\n    margin: 20px 0;\n    width: 100%;\n    max-width: 500px;\n  }\n\n  .features-section {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n    margin-top: 50px;\n    position: relative;\n  }\n}\n\n@media (max-width: 768px) {\n  .navigation {\n    flex-direction: column;\n    gap: 20px;\n    padding: 20px;\n  }\n\n  .nav-right {\n    gap: 20px;\n  }\n\n  .hero-section {\n    flex-direction: column;\n    padding: 0 20px;\n    text-align: center;\n  }\n\n  .hero-title {\n    font-size: 80px;\n  }\n\n  .hero-subtitle {\n    font-size: 20px;\n    max-width: none;\n  }\n\n  .earth-image-container {\n    width: 300px;\n    height: 300px;\n  }\n\n  .earth-image {\n    width: 200px;\n    height: 200px;\n  }\n\n  .earth-image::before {\n    font-size: 100px;\n  }\n}\n</style>\n"], "mappings": "AAkKA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA,GACA;QACAC,EAAA;QACAC,OAAA;MACA,GACA;QACAD,EAAA;QACAC,OAAA;MACA,GACA;QACAD,EAAA;QACAC,OAAA;MACA,GACA;QACAD,EAAA;QACAC,OAAA;MACA,GACA;QACAD,EAAA;QACAC,OAAA;MACA,GACA;QACAD,EAAA;QACAC,OAAA,EACA;MACA,EACA;MACAC,QAAA,GACA;QACAF,EAAA;QACAH,IAAA;MACA,GACA;QACAG,EAAA;QACAH,IAAA;MACA,GACA;QACAG,EAAA;QACAH,IAAA;MACA,EACA;MACAM,WAAA,GACA;QACAH,EAAA;QACAH,IAAA;QACAI,OAAA;QACAG,KAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA,GACA;QACAP,EAAA;QACAH,IAAA;QACAI,OAAA;QACAG,KAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA,GACA;QACAP,EAAA;QACAH,IAAA;QACAI,OAAA;QACAG,KAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA,GACA;QACAP,EAAA;QACAH,IAAA;QACAI,OAAA;QACAG,KAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA,GACA;QACAP,EAAA;QACAH,IAAA;QACAI,OAAA;QACAG,KAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA,GACA;QACAP,EAAA;QACAH,IAAA;QACAI,OAAA,EACA;QACAG,KAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA;IAEA;EACA;EACAC,OAAA;IACAC,aAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;IACA;IACAC,YAAAC,QAAA;MACA,KAAAH,QAAA,CAAAI,IAAA,aAAAD,QAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}