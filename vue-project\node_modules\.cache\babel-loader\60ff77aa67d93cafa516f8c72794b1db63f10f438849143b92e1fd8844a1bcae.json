{"ast": null, "code": "import Vue from 'vue';\nimport Vuex from 'vuex';\nVue.use(Vuex);\nexport default new Vuex.Store({\n  state: {\n    count: 0\n  },\n  getters: {\n    getCount: state => state.count\n  },\n  mutations: {\n    INCREMENT(state) {\n      state.count++;\n    },\n    DECREMENT(state) {\n      state.count--;\n    },\n    SET_COUNT(state, count) {\n      state.count = count;\n    }\n  },\n  actions: {\n    increment({\n      commit\n    }) {\n      commit('INCREMENT');\n    },\n    decrement({\n      commit\n    }) {\n      commit('DECREMENT');\n    },\n    setCount({\n      commit\n    }, count) {\n      commit('SET_COUNT', count);\n    }\n  },\n  modules: {}\n});", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "use", "Store", "state", "count", "getters", "getCount", "mutations", "INCREMENT", "DECREMENT", "SET_COUNT", "actions", "increment", "commit", "decrement", "setCount", "modules"], "sources": ["C:/work/testProduct/figma/vue-project/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\n\nVue.use(Vuex)\n\nexport default new Vuex.Store({\n  state: {\n    count: 0\n  },\n  getters: {\n    getCount: state => state.count\n  },\n  mutations: {\n    INCREMENT(state) {\n      state.count++\n    },\n    DECREMENT(state) {\n      state.count--\n    },\n    SET_COUNT(state, count) {\n      state.count = count\n    }\n  },\n  actions: {\n    increment({ commit }) {\n      commit('INCREMENT')\n    },\n    decrement({ commit }) {\n      commit('DECREMENT')\n    },\n    setCount({ commit }, count) {\n      commit('SET_COUNT', count)\n    }\n  },\n  modules: {\n  }\n})\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AAEvBD,GAAG,CAACE,GAAG,CAACD,IAAI,CAAC;AAEb,eAAe,IAAIA,IAAI,CAACE,KAAK,CAAC;EAC5BC,KAAK,EAAE;IACLC,KAAK,EAAE;EACT,CAAC;EACDC,OAAO,EAAE;IACPC,QAAQ,EAAEH,KAAK,IAAIA,KAAK,CAACC;EAC3B,CAAC;EACDG,SAAS,EAAE;IACTC,SAASA,CAACL,KAAK,EAAE;MACfA,KAAK,CAACC,KAAK,EAAE;IACf,CAAC;IACDK,SAASA,CAACN,KAAK,EAAE;MACfA,KAAK,CAACC,KAAK,EAAE;IACf,CAAC;IACDM,SAASA,CAACP,KAAK,EAAEC,KAAK,EAAE;MACtBD,KAAK,CAACC,KAAK,GAAGA,KAAK;IACrB;EACF,CAAC;EACDO,OAAO,EAAE;IACPC,SAASA,CAAC;MAAEC;IAAO,CAAC,EAAE;MACpBA,MAAM,CAAC,WAAW,CAAC;IACrB,CAAC;IACDC,SAASA,CAAC;MAAED;IAAO,CAAC,EAAE;MACpBA,MAAM,CAAC,WAAW,CAAC;IACrB,CAAC;IACDE,QAAQA,CAAC;MAAEF;IAAO,CAAC,EAAET,KAAK,EAAE;MAC1BS,MAAM,CAAC,WAAW,EAAET,KAAK,CAAC;IAC5B;EACF,CAAC;EACDY,OAAO,EAAE,CACT;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}