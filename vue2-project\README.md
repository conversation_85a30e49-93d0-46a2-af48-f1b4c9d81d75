# Vue2 项目

这是一个基于 Vue 2.x 的完整项目，集成了 Vue Router、Vuex、Axios 和 Element UI。

## 技术栈

- **Vue 2.7.16** - 渐进式 JavaScript 框架
- **Vue Router 3.6.5** - Vue.js 官方路由管理器
- **Vuex 3.6.2** - Vue.js 应用程序的状态管理模式
- **Axios 1.6.0** - 基于 Promise 的 HTTP 库
- **Element UI 2.15.14** - 基于 Vue 2.0 的桌面端组件库

## 项目结构

```
vue2-project/
├── public/
│   └── index.html          # HTML 模板
├── src/
│   ├── components/         # 公共组件
│   ├── views/             # 页面组件
│   │   ├── Home.vue       # 首页
│   │   └── About.vue      # 关于页面
│   ├── router/            # 路由配置
│   │   └── index.js       # 路由定义
│   ├── store/             # Vuex 状态管理
│   │   └── index.js       # 状态管理配置
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── babel.config.js        # Babel 配置
├── vue.config.js          # Vue CLI 配置
└── package.json           # 项目依赖
```

## 安装和运行

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run serve
```

### 构建生产版本
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 功能特性

### 1. Vue Router 路由管理
- 配置了首页和关于页面的路由
- 支持 history 模式
- 懒加载路由组件

### 2. Vuex 状态管理
- 集中式状态管理
- 包含 state、getters、mutations、actions
- 示例：计数器功能

### 3. Axios HTTP 请求
- 封装了 HTTP 请求
- 示例：获取远程 API 数据

### 4. Element UI 组件库
- 完整的 UI 组件库
- 包含导航菜单、卡片、按钮、消息提示等组件
- 响应式布局支持

## 开发说明

### 添加新页面
1. 在 `src/views/` 目录下创建新的 Vue 组件
2. 在 `src/router/index.js` 中添加路由配置
3. 在导航菜单中添加对应的菜单项

### 状态管理
- 在 `src/store/index.js` 中定义全局状态
- 使用 `mapState` 和 `mapActions` 辅助函数简化组件中的使用

### HTTP 请求
- 使用 Axios 进行 API 请求
- 建议在组件的 methods 中定义请求方法
- 使用 async/await 处理异步操作

## 浏览器支持

- 现代浏览器
- IE 11+（需要 polyfill）

## 许可证

MIT
