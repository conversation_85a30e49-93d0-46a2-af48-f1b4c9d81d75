{"ast": null, "code": "export default {\n  name: \"App\"\n};", "map": {"version": 3, "names": ["name"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"App\",\n};\n</script>\n\n<style>\n#app {\n  margin: 0;\n  padding: 0;\n  width: 100%;\n  min-height: 100vh;\n}\n\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n</style>\n"], "mappings": "AAOA;EACAA,IAAA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}