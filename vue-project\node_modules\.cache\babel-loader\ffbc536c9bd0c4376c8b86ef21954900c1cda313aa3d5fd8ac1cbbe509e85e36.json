{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\nimport Home from '../views/Home.vue';\nVue.use(VueRouter);\nconst routes = [{\n  path: '/',\n  name: 'Home',\n  component: Home\n}, {\n  path: '/about',\n  name: 'About',\n  // route level code-splitting\n  // this generates a separate chunk (about.[hash].js) for this route\n  // which is lazy-loaded when the route is visited.\n  component: () => import(/* webpackChunkName: \"about\" */'../views/About.vue')\n}];\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Home", "use", "routes", "path", "name", "component", "router", "mode", "base", "process", "env", "BASE_URL"], "sources": ["C:/work/testProduct/figma/vue-project/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport Home from '../views/Home.vue'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    name: 'Home',\n    component: Home\n  },\n  {\n    path: '/about',\n    name: 'About',\n    // route level code-splitting\n    // this generates a separate chunk (about.[hash].js) for this route\n    // which is lazy-loaded when the route is visited.\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/About.vue')\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\nexport default router\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,mBAAmB;AAEpCF,GAAG,CAACG,GAAG,CAACF,SAAS,CAAC;AAElB,MAAMG,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEL;AACb,CAAC,EACD;EACEG,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACb;EACA;EACA;EACAC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,oBAAoB;AAC9E,CAAC,CACF;AAED,MAAMC,MAAM,GAAG,IAAIP,SAAS,CAAC;EAC3BQ,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BT;AACF,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}