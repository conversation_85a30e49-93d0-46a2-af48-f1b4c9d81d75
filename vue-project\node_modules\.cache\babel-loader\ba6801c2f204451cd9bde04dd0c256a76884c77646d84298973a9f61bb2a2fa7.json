{"ast": null, "code": "export default {\n  name: \"App\"\n};", "map": {"version": 3, "names": ["name"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <el-container>\n      <el-header class=\"header\">\n        <el-menu\n          :default-active=\"$route.path\"\n          class=\"el-menu-demo\"\n          mode=\"horizontal\"\n          router\n          background-color=\"#545c64\"\n          text-color=\"#fff\"\n          active-text-color=\"#ffd04b\"\n        >\n          <el-menu-item index=\"/\">首页</el-menu-item>\n          <el-menu-item index=\"/about\">关于</el-menu-item>\n        </el-menu>\n      </el-header>\n      <el-main class=\"main\">\n        <router-view />\n      </el-main>\n    </el-container>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"App\",\n};\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  height: 100vh;\n}\n\n.header {\n  padding: 0;\n}\n\n.main {\n  padding: 0;\n}\n\n.el-menu-demo {\n  height: 60px;\n  line-height: 60px;\n}\n</style>\n"], "mappings": "AAyBA;EACAA,IAAA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}