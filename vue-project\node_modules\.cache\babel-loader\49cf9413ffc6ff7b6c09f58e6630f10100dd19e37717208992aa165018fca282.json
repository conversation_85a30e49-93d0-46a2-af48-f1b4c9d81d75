{"ast": null, "code": "export default {\n  name: \"AboutPage\"\n};", "map": {"version": 3, "names": ["name"], "sources": ["src/views/About.vue"], "sourcesContent": ["<template>\n  <div class=\"about\">\n    <el-container>\n      <el-header>\n        <h1>关于页面</h1>\n      </el-header>\n      <el-main>\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>项目信息</span>\n          </div>\n          <div class=\"text item\">\n            <el-descriptions title=\"技术栈\" :column=\"1\" border>\n              <el-descriptions-item label=\"前端框架\"\n                >Vue 2.x</el-descriptions-item\n              >\n              <el-descriptions-item label=\"路由管理\"\n                >Vue Router 3.x</el-descriptions-item\n              >\n              <el-descriptions-item label=\"状态管理\"\n                >Vuex 3.x</el-descriptions-item\n              >\n              <el-descriptions-item label=\"HTTP客户端\"\n                >Axios</el-descriptions-item\n              >\n              <el-descriptions-item label=\"UI组件库\"\n                >Element UI</el-descriptions-item\n              >\n            </el-descriptions>\n          </div>\n        </el-card>\n\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>Element UI 组件示例</span>\n          </div>\n          <div class=\"text item\">\n            <el-alert\n              title=\"成功提示\"\n              type=\"success\"\n              :closable=\"false\"\n              show-icon\n            >\n            </el-alert>\n            <br />\n            <el-button-group>\n              <el-button type=\"primary\" icon=\"el-icon-arrow-left\"\n                >上一页</el-button\n              >\n              <el-button type=\"primary\"\n                >下一页<i class=\"el-icon-arrow-right el-icon--right\"></i\n              ></el-button>\n            </el-button-group>\n          </div>\n        </el-card>\n      </el-main>\n    </el-container>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"AboutPage\",\n};\n</script>\n\n<style scoped>\n.about {\n  padding: 20px;\n}\n\n.box-card {\n  margin-bottom: 20px;\n}\n\n.text {\n  font-size: 14px;\n}\n\n.item {\n  margin-bottom: 18px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n.clearfix:after {\n  clear: both;\n}\n</style>\n"], "mappings": "AA6DA;EACAA,IAAA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}