{"ast": null, "code": "export default {\n  name: \"HomePage\",\n  data() {\n    return {\n      // Data for the Save Earth page\n    };\n  },\n  methods: {\n    calculateImpact() {\n      this.$message.success(\"Calculate Impact feature coming soon!\");\n      console.log(\"Calculate Impact clicked\");\n    },\n    discoverMore(type) {\n      this.$message.info(`Discover more about ${type}`);\n      console.log(`Discover more clicked for: ${type}`);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "methods", "calculateImpact", "$message", "success", "console", "log", "discoverMore", "type", "info"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\n  <div class=\"save-earth-home\">\n    <!-- Background with gradient -->\n    <div class=\"background-gradient\"></div>\n\n    <!-- Large background text -->\n    <div class=\"background-text\">SAVE<br />EARTH</div>\n\n    <!-- Main content container -->\n    <div class=\"main-container\">\n      <!-- Navigation Header -->\n      <header class=\"navigation\">\n        <div class=\"nav-left\">\n          <span class=\"earth-emoji\">🌎</span>\n          <span class=\"brand-text\">Save Earth</span>\n          <span class=\"earth-emoji-overlay\">🌎</span>\n        </div>\n        <nav class=\"nav-right\">\n          <a href=\"#\" class=\"nav-link\">About</a>\n          <a href=\"#\" class=\"nav-link\">Articles</a>\n          <a href=\"#\" class=\"nav-link\">For Business</a>\n          <a href=\"#\" class=\"nav-link\">Showcase</a>\n        </nav>\n      </header>\n\n      <!-- Hero Section -->\n      <section class=\"hero-section\">\n        <div class=\"hero-content\">\n          <h1 class=\"hero-title\">Mother Earth</h1>\n          <p class=\"hero-subtitle\">We help you live carbon neutral</p>\n\n          <!-- Calculate Impact Button -->\n          <button class=\"calculate-btn\" @click=\"calculateImpact\">\n            Calculate Impact\n          </button>\n        </div>\n\n        <!-- Hero Image -->\n        <div class=\"hero-image\">\n          <div class=\"earth-image-container\">\n            <div class=\"earth-image\"></div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Features Section -->\n      <section class=\"features-section\">\n        <!-- Feature 1: Understand Emission -->\n        <div class=\"feature-card feature-left\">\n          <div class=\"feature-content\">\n            <h3 class=\"feature-title\">Understand Emission</h3>\n            <p class=\"feature-description\">\n              Use our calculation powered by data from world bank to estimate\n              your emission\n            </p>\n            <div class=\"discover-more\" @click=\"discoverMore('emission')\">\n              <span class=\"discover-text\">Discover More</span>\n              <div class=\"discover-button\">\n                <div class=\"arrow-icon\">→</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Feature 2: Support Climate Projects -->\n        <div class=\"feature-card feature-right\">\n          <div class=\"feature-content\">\n            <h3 class=\"feature-title\">Support Climate Projects</h3>\n            <p class=\"feature-description\">\n              Sign up and and fund high impact carbon offsetting & plastic\n              recycling initiatives.\n            </p>\n            <div class=\"discover-more\" @click=\"discoverMore('projects')\">\n              <span class=\"discover-text\">Discover More</span>\n              <div class=\"discover-button\">\n                <div class=\"arrow-icon\">→</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Decorative Elements -->\n      <div class=\"decorative-blur green-blur\"></div>\n      <div class=\"decorative-blur red-blur\"></div>\n    </div>\n\n    <!-- Footer -->\n    <footer class=\"footer\">\n      <span class=\"footer-text\">www.nickelfox.com</span>\n    </footer>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"HomePage\",\n  data() {\n    return {\n      // Data for the Save Earth page\n    };\n  },\n  methods: {\n    calculateImpact() {\n      this.$message.success(\"Calculate Impact feature coming soon!\");\n      console.log(\"Calculate Impact clicked\");\n    },\n    discoverMore(type) {\n      this.$message.info(`Discover more about ${type}`);\n      console.log(`Discover more clicked for: ${type}`);\n    },\n  },\n};\n</script>\n\n<style scoped>\n.home {\n  padding: 20px;\n}\n\n.box-card {\n  margin-bottom: 20px;\n}\n\n.text {\n  font-size: 14px;\n}\n\n.item {\n  margin-bottom: 18px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n.clearfix:after {\n  clear: both;\n}\n</style>\n"], "mappings": "AA+FA;EACAA,IAAA;EACAC,KAAA;IACA;MACA;IAAA,CACA;EACA;EACAC,OAAA;IACAC,gBAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;MACAC,OAAA,CAAAC,GAAA;IACA;IACAC,aAAAC,IAAA;MACA,KAAAL,QAAA,CAAAM,IAAA,wBAAAD,IAAA;MACAH,OAAA,CAAAC,GAAA,+BAAAE,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}