{"ast": null, "code": "\"use strict\";\n\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.for-each.js\");\nexports.__esModule = true;\nfunction _broadcast(componentName, eventName, params) {\n  this.$children.forEach(function (child) {\n    var name = child.$options.componentName;\n    if (name === componentName) {\n      child.$emit.apply(child, [eventName].concat(params));\n    } else {\n      _broadcast.apply(child, [componentName, eventName].concat([params]));\n    }\n  });\n}\nexports.default = {\n  methods: {\n    dispatch: function dispatch(componentName, eventName, params) {\n      var parent = this.$parent || this.$root;\n      var name = parent.$options.componentName;\n      while (parent && (!name || name !== componentName)) {\n        parent = parent.$parent;\n        if (parent) {\n          name = parent.$options.componentName;\n        }\n      }\n      if (parent) {\n        parent.$emit.apply(parent, [eventName].concat(params));\n      }\n    },\n    broadcast: function broadcast(componentName, eventName, params) {\n      _broadcast.call(this, componentName, eventName, params);\n    }\n  }\n};", "map": {"version": 3, "names": ["require", "exports", "__esModule", "_broadcast", "componentName", "eventName", "params", "$children", "for<PERSON>ach", "child", "name", "$options", "$emit", "apply", "concat", "default", "methods", "dispatch", "parent", "$parent", "$root", "broadcast", "call"], "sources": ["C:/work/testProduct/figma/vue-project/node_modules/element-ui/lib/mixins/emitter.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nfunction _broadcast(componentName, eventName, params) {\n  this.$children.forEach(function (child) {\n    var name = child.$options.componentName;\n\n    if (name === componentName) {\n      child.$emit.apply(child, [eventName].concat(params));\n    } else {\n      _broadcast.apply(child, [componentName, eventName].concat([params]));\n    }\n  });\n}\nexports.default = {\n  methods: {\n    dispatch: function dispatch(componentName, eventName, params) {\n      var parent = this.$parent || this.$root;\n      var name = parent.$options.componentName;\n\n      while (parent && (!name || name !== componentName)) {\n        parent = parent.$parent;\n\n        if (parent) {\n          name = parent.$options.componentName;\n        }\n      }\n      if (parent) {\n        parent.$emit.apply(parent, [eventName].concat(params));\n      }\n    },\n    broadcast: function broadcast(componentName, eventName, params) {\n      _broadcast.call(this, componentName, eventName, params);\n    }\n  }\n};"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAEbC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzB,SAASC,UAAUA,CAACC,aAAa,EAAEC,SAAS,EAAEC,MAAM,EAAE;EACpD,IAAI,CAACC,SAAS,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;IACtC,IAAIC,IAAI,GAAGD,KAAK,CAACE,QAAQ,CAACP,aAAa;IAEvC,IAAIM,IAAI,KAAKN,aAAa,EAAE;MAC1BK,KAAK,CAACG,KAAK,CAACC,KAAK,CAACJ,KAAK,EAAE,CAACJ,SAAS,CAAC,CAACS,MAAM,CAACR,MAAM,CAAC,CAAC;IACtD,CAAC,MAAM;MACLH,UAAU,CAACU,KAAK,CAACJ,KAAK,EAAE,CAACL,aAAa,EAAEC,SAAS,CAAC,CAACS,MAAM,CAAC,CAACR,MAAM,CAAC,CAAC,CAAC;IACtE;EACF,CAAC,CAAC;AACJ;AACAL,OAAO,CAACc,OAAO,GAAG;EAChBC,OAAO,EAAE;IACPC,QAAQ,EAAE,SAASA,QAAQA,CAACb,aAAa,EAAEC,SAAS,EAAEC,MAAM,EAAE;MAC5D,IAAIY,MAAM,GAAG,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,KAAK;MACvC,IAAIV,IAAI,GAAGQ,MAAM,CAACP,QAAQ,CAACP,aAAa;MAExC,OAAOc,MAAM,KAAK,CAACR,IAAI,IAAIA,IAAI,KAAKN,aAAa,CAAC,EAAE;QAClDc,MAAM,GAAGA,MAAM,CAACC,OAAO;QAEvB,IAAID,MAAM,EAAE;UACVR,IAAI,GAAGQ,MAAM,CAACP,QAAQ,CAACP,aAAa;QACtC;MACF;MACA,IAAIc,MAAM,EAAE;QACVA,MAAM,CAACN,KAAK,CAACC,KAAK,CAACK,MAAM,EAAE,CAACb,SAAS,CAAC,CAACS,MAAM,CAACR,MAAM,CAAC,CAAC;MACxD;IACF,CAAC;IACDe,SAAS,EAAE,SAASA,SAASA,CAACjB,aAAa,EAAEC,SAAS,EAAEC,MAAM,EAAE;MAC9DH,UAAU,CAACmB,IAAI,CAAC,IAAI,EAAElB,aAAa,EAAEC,SAAS,EAAEC,MAAM,CAAC;IACzD;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}