{"ast": null, "code": "/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param  {Number}    delay          A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param  {Boolean}   [noTrailing]   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds while the\n *                                    throttled-function is being called. If noTrailing is false or unspecified, callback will be executed one final time\n *                                    after the last throttled-function call. (After the throttled-function has not been called for `delay` milliseconds,\n *                                    the internal counter is reset)\n * @param  {Function}  callback       A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                    to `callback` when the throttled-function is executed.\n * @param  {Boolean}   [debounceMode] If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is false (at end),\n *                                    schedule `callback` to execute after `delay` ms.\n *\n * @return {Function}  A new, throttled, function.\n */\nmodule.exports = function (delay, noTrailing, callback, debounceMode) {\n  // After wrapper has stopped being called, this timeout ensures that\n  // `callback` is executed at the proper times in `throttle` and `end`\n  // debounce modes.\n  var timeoutID;\n\n  // Keep track of the last time `callback` was executed.\n  var lastExec = 0;\n\n  // `noTrailing` defaults to falsy.\n  if (typeof noTrailing !== 'boolean') {\n    debounceMode = callback;\n    callback = noTrailing;\n    noTrailing = undefined;\n  }\n\n  // The `wrapper` function encapsulates all of the throttling / debouncing\n  // functionality and when executed will limit the rate at which `callback`\n  // is executed.\n  function wrapper() {\n    var self = this;\n    var elapsed = Number(new Date()) - lastExec;\n    var args = arguments;\n\n    // Execute `callback` and update the `lastExec` timestamp.\n    function exec() {\n      lastExec = Number(new Date());\n      callback.apply(self, args);\n    }\n\n    // If `debounceMode` is true (at begin) this is used to clear the flag\n    // to allow future `callback` executions.\n    function clear() {\n      timeoutID = undefined;\n    }\n    if (debounceMode && !timeoutID) {\n      // Since `wrapper` is being called for the first time and\n      // `debounceMode` is true (at begin), execute `callback`.\n      exec();\n    }\n\n    // Clear any existing timeout.\n    if (timeoutID) {\n      clearTimeout(timeoutID);\n    }\n    if (debounceMode === undefined && elapsed > delay) {\n      // In throttle mode, if `delay` time has been exceeded, execute\n      // `callback`.\n      exec();\n    } else if (noTrailing !== true) {\n      // In trailing throttle mode, since `delay` time has not been\n      // exceeded, schedule `callback` to execute `delay` ms after most\n      // recent execution.\n      //\n      // If `debounceMode` is true (at begin), schedule `clear` to execute\n      // after `delay` ms.\n      //\n      // If `debounceMode` is false (at end), schedule `callback` to\n      // execute after `delay` ms.\n      timeoutID = setTimeout(debounceMode ? clear : exec, debounceMode === undefined ? delay - elapsed : delay);\n    }\n  }\n\n  // Return the wrapper function.\n  return wrapper;\n};", "map": {"version": 3, "names": ["module", "exports", "delay", "noTrailing", "callback", "debounceMode", "timeoutID", "lastExec", "undefined", "wrapper", "self", "elapsed", "Number", "Date", "args", "arguments", "exec", "apply", "clear", "clearTimeout", "setTimeout"], "sources": ["C:/work/testProduct/figma/vue-project/node_modules/throttle-debounce/throttle.js"], "sourcesContent": ["/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param  {Number}    delay          A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param  {Boolean}   [noTrailing]   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds while the\n *                                    throttled-function is being called. If noTrailing is false or unspecified, callback will be executed one final time\n *                                    after the last throttled-function call. (After the throttled-function has not been called for `delay` milliseconds,\n *                                    the internal counter is reset)\n * @param  {Function}  callback       A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                    to `callback` when the throttled-function is executed.\n * @param  {Boolean}   [debounceMode] If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is false (at end),\n *                                    schedule `callback` to execute after `delay` ms.\n *\n * @return {Function}  A new, throttled, function.\n */\nmodule.exports = function ( delay, noTrailing, callback, debounceMode ) {\n\n\t// After wrapper has stopped being called, this timeout ensures that\n\t// `callback` is executed at the proper times in `throttle` and `end`\n\t// debounce modes.\n\tvar timeoutID;\n\n\t// Keep track of the last time `callback` was executed.\n\tvar lastExec = 0;\n\n\t// `noTrailing` defaults to falsy.\n\tif ( typeof noTrailing !== 'boolean' ) {\n\t\tdebounceMode = callback;\n\t\tcallback = noTrailing;\n\t\tnoTrailing = undefined;\n\t}\n\n\t// The `wrapper` function encapsulates all of the throttling / debouncing\n\t// functionality and when executed will limit the rate at which `callback`\n\t// is executed.\n\tfunction wrapper () {\n\n\t\tvar self = this;\n\t\tvar elapsed = Number(new Date()) - lastExec;\n\t\tvar args = arguments;\n\n\t\t// Execute `callback` and update the `lastExec` timestamp.\n\t\tfunction exec () {\n\t\t\tlastExec = Number(new Date());\n\t\t\tcallback.apply(self, args);\n\t\t}\n\n\t\t// If `debounceMode` is true (at begin) this is used to clear the flag\n\t\t// to allow future `callback` executions.\n\t\tfunction clear () {\n\t\t\ttimeoutID = undefined;\n\t\t}\n\n\t\tif ( debounceMode && !timeoutID ) {\n\t\t\t// Since `wrapper` is being called for the first time and\n\t\t\t// `debounceMode` is true (at begin), execute `callback`.\n\t\t\texec();\n\t\t}\n\n\t\t// Clear any existing timeout.\n\t\tif ( timeoutID ) {\n\t\t\tclearTimeout(timeoutID);\n\t\t}\n\n\t\tif ( debounceMode === undefined && elapsed > delay ) {\n\t\t\t// In throttle mode, if `delay` time has been exceeded, execute\n\t\t\t// `callback`.\n\t\t\texec();\n\n\t\t} else if ( noTrailing !== true ) {\n\t\t\t// In trailing throttle mode, since `delay` time has not been\n\t\t\t// exceeded, schedule `callback` to execute `delay` ms after most\n\t\t\t// recent execution.\n\t\t\t//\n\t\t\t// If `debounceMode` is true (at begin), schedule `clear` to execute\n\t\t\t// after `delay` ms.\n\t\t\t//\n\t\t\t// If `debounceMode` is false (at end), schedule `callback` to\n\t\t\t// execute after `delay` ms.\n\t\t\ttimeoutID = setTimeout(debounceMode ? clear : exec, debounceMode === undefined ? delay - elapsed : delay);\n\t\t}\n\n\t}\n\n\t// Return the wrapper function.\n\treturn wrapper;\n\n};\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,UAAWC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,EAAG;EAEvE;EACA;EACA;EACA,IAAIC,SAAS;;EAEb;EACA,IAAIC,QAAQ,GAAG,CAAC;;EAEhB;EACA,IAAK,OAAOJ,UAAU,KAAK,SAAS,EAAG;IACtCE,YAAY,GAAGD,QAAQ;IACvBA,QAAQ,GAAGD,UAAU;IACrBA,UAAU,GAAGK,SAAS;EACvB;;EAEA;EACA;EACA;EACA,SAASC,OAAOA,CAAA,EAAI;IAEnB,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIC,OAAO,GAAGC,MAAM,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC,GAAGN,QAAQ;IAC3C,IAAIO,IAAI,GAAGC,SAAS;;IAEpB;IACA,SAASC,IAAIA,CAAA,EAAI;MAChBT,QAAQ,GAAGK,MAAM,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;MAC7BT,QAAQ,CAACa,KAAK,CAACP,IAAI,EAAEI,IAAI,CAAC;IAC3B;;IAEA;IACA;IACA,SAASI,KAAKA,CAAA,EAAI;MACjBZ,SAAS,GAAGE,SAAS;IACtB;IAEA,IAAKH,YAAY,IAAI,CAACC,SAAS,EAAG;MACjC;MACA;MACAU,IAAI,CAAC,CAAC;IACP;;IAEA;IACA,IAAKV,SAAS,EAAG;MAChBa,YAAY,CAACb,SAAS,CAAC;IACxB;IAEA,IAAKD,YAAY,KAAKG,SAAS,IAAIG,OAAO,GAAGT,KAAK,EAAG;MACpD;MACA;MACAc,IAAI,CAAC,CAAC;IAEP,CAAC,MAAM,IAAKb,UAAU,KAAK,IAAI,EAAG;MACjC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAG,SAAS,GAAGc,UAAU,CAACf,YAAY,GAAGa,KAAK,GAAGF,IAAI,EAAEX,YAAY,KAAKG,SAAS,GAAGN,KAAK,GAAGS,OAAO,GAAGT,KAAK,CAAC;IAC1G;EAED;;EAEA;EACA,OAAOO,OAAO;AAEf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}