{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"about\"\n  }, [_c(\"el-container\", [_c(\"el-header\", [_c(\"h1\", [_vm._v(\"关于页面\")])]), _c(\"el-main\", [_c(\"el-card\", {\n    staticClass: \"box-card\"\n  }, [_c(\"div\", {\n    staticClass: \"clearfix\",\n    attrs: {\n      slot: \"header\"\n    },\n    slot: \"header\"\n  }, [_c(\"span\", [_vm._v(\"项目信息\")])]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_c(\"el-descriptions\", {\n    attrs: {\n      title: \"技术栈\",\n      column: 1,\n      border: \"\"\n    }\n  }, [_c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"前端框架\"\n    }\n  }, [_vm._v(\"Vue 2.x\")]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"路由管理\"\n    }\n  }, [_vm._v(\"Vue Router 3.x\")]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"状态管理\"\n    }\n  }, [_vm._v(\"Vuex 3.x\")]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"HTTP客户端\"\n    }\n  }, [_vm._v(\"Axios\")]), _c(\"el-descriptions-item\", {\n    attrs: {\n      label: \"UI组件库\"\n    }\n  }, [_vm._v(\"Element UI\")])], 1)], 1)]), _c(\"el-card\", {\n    staticClass: \"box-card\"\n  }, [_c(\"div\", {\n    staticClass: \"clearfix\",\n    attrs: {\n      slot: \"header\"\n    },\n    slot: \"header\"\n  }, [_c(\"span\", [_vm._v(\"Element UI 组件示例\")])]), _c(\"div\", {\n    staticClass: \"text item\"\n  }, [_c(\"el-alert\", {\n    attrs: {\n      title: \"成功提示\",\n      type: \"success\",\n      closable: false,\n      \"show-icon\": \"\"\n    }\n  }), _c(\"br\"), _c(\"el-button-group\", [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      icon: \"el-icon-arrow-left\"\n    }\n  }, [_vm._v(\"上一页\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\"\n    }\n  }, [_vm._v(\"下一页\"), _c(\"i\", {\n    staticClass: \"el-icon-arrow-right el-icon--right\"\n  })])], 1)], 1)])], 1)], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "attrs", "slot", "title", "column", "border", "label", "type", "closable", "icon", "staticRenderFns", "_withStripped"], "sources": ["C:/work/testProduct/figma/vue-project/src/views/About.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"about\" },\n    [\n      _c(\n        \"el-container\",\n        [\n          _c(\"el-header\", [_c(\"h1\", [_vm._v(\"关于页面\")])]),\n          _c(\n            \"el-main\",\n            [\n              _c(\"el-card\", { staticClass: \"box-card\" }, [\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"clearfix\",\n                    attrs: { slot: \"header\" },\n                    slot: \"header\",\n                  },\n                  [_c(\"span\", [_vm._v(\"项目信息\")])]\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"text item\" },\n                  [\n                    _c(\n                      \"el-descriptions\",\n                      { attrs: { title: \"技术栈\", column: 1, border: \"\" } },\n                      [\n                        _c(\n                          \"el-descriptions-item\",\n                          { attrs: { label: \"前端框架\" } },\n                          [_vm._v(\"Vue 2.x\")]\n                        ),\n                        _c(\n                          \"el-descriptions-item\",\n                          { attrs: { label: \"路由管理\" } },\n                          [_vm._v(\"Vue Router 3.x\")]\n                        ),\n                        _c(\n                          \"el-descriptions-item\",\n                          { attrs: { label: \"状态管理\" } },\n                          [_vm._v(\"Vuex 3.x\")]\n                        ),\n                        _c(\n                          \"el-descriptions-item\",\n                          { attrs: { label: \"HTTP客户端\" } },\n                          [_vm._v(\"Axios\")]\n                        ),\n                        _c(\n                          \"el-descriptions-item\",\n                          { attrs: { label: \"UI组件库\" } },\n                          [_vm._v(\"Element UI\")]\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n              ]),\n              _c(\"el-card\", { staticClass: \"box-card\" }, [\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"clearfix\",\n                    attrs: { slot: \"header\" },\n                    slot: \"header\",\n                  },\n                  [_c(\"span\", [_vm._v(\"Element UI 组件示例\")])]\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"text item\" },\n                  [\n                    _c(\"el-alert\", {\n                      attrs: {\n                        title: \"成功提示\",\n                        type: \"success\",\n                        closable: false,\n                        \"show-icon\": \"\",\n                      },\n                    }),\n                    _c(\"br\"),\n                    _c(\n                      \"el-button-group\",\n                      [\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: {\n                              type: \"primary\",\n                              icon: \"el-icon-arrow-left\",\n                            },\n                          },\n                          [_vm._v(\"上一页\")]\n                        ),\n                        _c(\"el-button\", { attrs: { type: \"primary\" } }, [\n                          _vm._v(\"下一页\"),\n                          _c(\"i\", {\n                            staticClass: \"el-icon-arrow-right el-icon--right\",\n                          }),\n                        ]),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n              ]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAQ,CAAC,EACxB,CACEF,EAAE,CACA,cAAc,EACd,CACEA,EAAE,CAAC,WAAW,EAAE,CAACA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7CH,EAAE,CACA,SAAS,EACT,CACEA,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,UAAU;IACvBE,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CAACL,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC/B,CAAC,EACDH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CACA,iBAAiB,EACjB;IAAEI,KAAK,EAAE;MAAEE,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAG;EAAE,CAAC,EAClD,CACER,EAAE,CACA,sBAAsB,EACtB;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CAACV,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,EACDH,EAAE,CACA,sBAAsB,EACtB;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CAACV,GAAG,CAACI,EAAE,CAAC,gBAAgB,CAAC,CAC3B,CAAC,EACDH,EAAE,CACA,sBAAsB,EACtB;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CAACV,GAAG,CAACI,EAAE,CAAC,UAAU,CAAC,CACrB,CAAC,EACDH,EAAE,CACA,sBAAsB,EACtB;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAU;EAAE,CAAC,EAC/B,CAACV,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAClB,CAAC,EACDH,EAAE,CACA,sBAAsB,EACtB;IAAEI,KAAK,EAAE;MAAEK,KAAK,EAAE;IAAQ;EAAE,CAAC,EAC7B,CAACV,GAAG,CAACI,EAAE,CAAC,YAAY,CAAC,CACvB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFH,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,UAAU;IACvBE,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CAACL,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAC1C,CAAC,EACDH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLE,KAAK,EAAE,MAAM;MACbI,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE,KAAK;MACf,WAAW,EAAE;IACf;EACF,CAAC,CAAC,EACFX,EAAE,CAAC,IAAI,CAAC,EACRA,EAAE,CACA,iBAAiB,EACjB,CACEA,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MACLM,IAAI,EAAE,SAAS;MACfE,IAAI,EAAE;IACR;EACF,CAAC,EACD,CAACb,GAAG,CAACI,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,EACDH,EAAE,CAAC,WAAW,EAAE;IAAEI,KAAK,EAAE;MAAEM,IAAI,EAAE;IAAU;EAAE,CAAC,EAAE,CAC9CX,GAAG,CAACI,EAAE,CAAC,KAAK,CAAC,EACbH,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIW,eAAe,GAAG,EAAE;AACxBf,MAAM,CAACgB,aAAa,GAAG,IAAI;AAE3B,SAAShB,MAAM,EAAEe,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}