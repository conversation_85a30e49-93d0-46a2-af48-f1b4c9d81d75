{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"save-earth-home\"\n  }, [_c(\"div\", {\n    staticClass: \"background-gradient\"\n  }), _vm._m(0), _c(\"div\", {\n    staticClass: \"main-container\"\n  }, [_vm._m(1), _c(\"section\", {\n    staticClass: \"hero-section\"\n  }, [_c(\"div\", {\n    staticClass: \"hero-content\"\n  }, [_c(\"h1\", {\n    staticClass: \"hero-title\"\n  }, [_vm._v(\"Mother Earth\")]), _c(\"p\", {\n    staticClass: \"hero-subtitle\"\n  }, [_vm._v(\"We help you live carbon neutral\")]), _c(\"button\", {\n    staticClass: \"calculate-btn\",\n    on: {\n      click: _vm.calculateImpact\n    }\n  }, [_vm._v(\" Calculate Impact \")])]), _vm._m(2)]), _c(\"section\", {\n    staticClass: \"features-section\"\n  }, [_c(\"div\", {\n    staticClass: \"feature-card feature-left\"\n  }, [_c(\"div\", {\n    staticClass: \"feature-content\"\n  }, [_c(\"h3\", {\n    staticClass: \"feature-title\"\n  }, [_vm._v(\"Understand Emission\")]), _c(\"p\", {\n    staticClass: \"feature-description\"\n  }, [_vm._v(\" Use our calculation powered by data from world bank to estimate your emission \")]), _c(\"div\", {\n    staticClass: \"discover-more\",\n    on: {\n      click: function ($event) {\n        return _vm.discoverMore(\"emission\");\n      }\n    }\n  }, [_c(\"span\", {\n    staticClass: \"discover-text\"\n  }, [_vm._v(\"Discover More\")]), _vm._m(3)])])]), _c(\"div\", {\n    staticClass: \"feature-card feature-right\"\n  }, [_c(\"div\", {\n    staticClass: \"feature-content\"\n  }, [_c(\"h3\", {\n    staticClass: \"feature-title\"\n  }, [_vm._v(\"Support Climate Projects\")]), _c(\"p\", {\n    staticClass: \"feature-description\"\n  }, [_vm._v(\" Sign up and and fund high impact carbon offsetting & plastic recycling initiatives. \")]), _c(\"div\", {\n    staticClass: \"discover-more\",\n    on: {\n      click: function ($event) {\n        return _vm.discoverMore(\"projects\");\n      }\n    }\n  }, [_c(\"span\", {\n    staticClass: \"discover-text\"\n  }, [_vm._v(\"Discover More\")]), _vm._m(4)])])])]), _c(\"div\", {\n    staticClass: \"decorative-blur green-blur\"\n  }), _c(\"div\", {\n    staticClass: \"decorative-blur red-blur\"\n  })]), _vm._m(5)]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"background-text\"\n  }, [_vm._v(\"SAVE\"), _c(\"br\"), _vm._v(\"EARTH\")]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"header\", {\n    staticClass: \"navigation\"\n  }, [_c(\"div\", {\n    staticClass: \"nav-left\"\n  }, [_c(\"span\", {\n    staticClass: \"earth-emoji\"\n  }, [_vm._v(\"🌎\")]), _c(\"span\", {\n    staticClass: \"brand-text\"\n  }, [_vm._v(\"Save Earth\")]), _c(\"span\", {\n    staticClass: \"earth-emoji-overlay\"\n  }, [_vm._v(\"🌎\")])]), _c(\"nav\", {\n    staticClass: \"nav-right\"\n  }, [_c(\"a\", {\n    staticClass: \"nav-link\",\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"About\")]), _c(\"a\", {\n    staticClass: \"nav-link\",\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"Articles\")]), _c(\"a\", {\n    staticClass: \"nav-link\",\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"For Business\")]), _c(\"a\", {\n    staticClass: \"nav-link\",\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"Showcase\")])])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hero-image\"\n  }, [_c(\"div\", {\n    staticClass: \"earth-image-container\"\n  }, [_c(\"div\", {\n    staticClass: \"earth-image\"\n  })])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"discover-button\"\n  }, [_c(\"div\", {\n    staticClass: \"arrow-icon\"\n  }, [_vm._v(\"→\")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"discover-button\"\n  }, [_c(\"div\", {\n    staticClass: \"arrow-icon\"\n  }, [_vm._v(\"→\")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"footer\", {\n    staticClass: \"footer\"\n  }, [_c(\"span\", {\n    staticClass: \"footer-text\"\n  }, [_vm._v(\"www.nickelfox.com\")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "_v", "on", "click", "calculateImpact", "$event", "discoverMore", "staticRenderFns", "attrs", "href", "_withStripped"], "sources": ["C:/work/testProduct/figma/vue-project/src/views/Home.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"save-earth-home\" }, [\n    _c(\"div\", { staticClass: \"background-gradient\" }),\n    _vm._m(0),\n    _c(\"div\", { staticClass: \"main-container\" }, [\n      _vm._m(1),\n      _c(\"section\", { staticClass: \"hero-section\" }, [\n        _c(\"div\", { staticClass: \"hero-content\" }, [\n          _c(\"h1\", { staticClass: \"hero-title\" }, [_vm._v(\"Mother Earth\")]),\n          _c(\"p\", { staticClass: \"hero-subtitle\" }, [\n            _vm._v(\"We help you live carbon neutral\"),\n          ]),\n          _c(\n            \"button\",\n            {\n              staticClass: \"calculate-btn\",\n              on: { click: _vm.calculateImpact },\n            },\n            [_vm._v(\" Calculate Impact \")]\n          ),\n        ]),\n        _vm._m(2),\n      ]),\n      _c(\"section\", { staticClass: \"features-section\" }, [\n        _c(\"div\", { staticClass: \"feature-card feature-left\" }, [\n          _c(\"div\", { staticClass: \"feature-content\" }, [\n            _c(\"h3\", { staticClass: \"feature-title\" }, [\n              _vm._v(\"Understand Emission\"),\n            ]),\n            _c(\"p\", { staticClass: \"feature-description\" }, [\n              _vm._v(\n                \" Use our calculation powered by data from world bank to estimate your emission \"\n              ),\n            ]),\n            _c(\n              \"div\",\n              {\n                staticClass: \"discover-more\",\n                on: {\n                  click: function ($event) {\n                    return _vm.discoverMore(\"emission\")\n                  },\n                },\n              },\n              [\n                _c(\"span\", { staticClass: \"discover-text\" }, [\n                  _vm._v(\"Discover More\"),\n                ]),\n                _vm._m(3),\n              ]\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"feature-card feature-right\" }, [\n          _c(\"div\", { staticClass: \"feature-content\" }, [\n            _c(\"h3\", { staticClass: \"feature-title\" }, [\n              _vm._v(\"Support Climate Projects\"),\n            ]),\n            _c(\"p\", { staticClass: \"feature-description\" }, [\n              _vm._v(\n                \" Sign up and and fund high impact carbon offsetting & plastic recycling initiatives. \"\n              ),\n            ]),\n            _c(\n              \"div\",\n              {\n                staticClass: \"discover-more\",\n                on: {\n                  click: function ($event) {\n                    return _vm.discoverMore(\"projects\")\n                  },\n                },\n              },\n              [\n                _c(\"span\", { staticClass: \"discover-text\" }, [\n                  _vm._v(\"Discover More\"),\n                ]),\n                _vm._m(4),\n              ]\n            ),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"decorative-blur green-blur\" }),\n      _c(\"div\", { staticClass: \"decorative-blur red-blur\" }),\n    ]),\n    _vm._m(5),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"background-text\" }, [\n      _vm._v(\"SAVE\"),\n      _c(\"br\"),\n      _vm._v(\"EARTH\"),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"header\", { staticClass: \"navigation\" }, [\n      _c(\"div\", { staticClass: \"nav-left\" }, [\n        _c(\"span\", { staticClass: \"earth-emoji\" }, [_vm._v(\"🌎\")]),\n        _c(\"span\", { staticClass: \"brand-text\" }, [_vm._v(\"Save Earth\")]),\n        _c(\"span\", { staticClass: \"earth-emoji-overlay\" }, [_vm._v(\"🌎\")]),\n      ]),\n      _c(\"nav\", { staticClass: \"nav-right\" }, [\n        _c(\"a\", { staticClass: \"nav-link\", attrs: { href: \"#\" } }, [\n          _vm._v(\"About\"),\n        ]),\n        _c(\"a\", { staticClass: \"nav-link\", attrs: { href: \"#\" } }, [\n          _vm._v(\"Articles\"),\n        ]),\n        _c(\"a\", { staticClass: \"nav-link\", attrs: { href: \"#\" } }, [\n          _vm._v(\"For Business\"),\n        ]),\n        _c(\"a\", { staticClass: \"nav-link\", attrs: { href: \"#\" } }, [\n          _vm._v(\"Showcase\"),\n        ]),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"hero-image\" }, [\n      _c(\"div\", { staticClass: \"earth-image-container\" }, [\n        _c(\"div\", { staticClass: \"earth-image\" }),\n      ]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"discover-button\" }, [\n      _c(\"div\", { staticClass: \"arrow-icon\" }, [_vm._v(\"→\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"discover-button\" }, [\n      _c(\"div\", { staticClass: \"arrow-icon\" }, [_vm._v(\"→\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"footer\", { staticClass: \"footer\" }, [\n      _c(\"span\", { staticClass: \"footer-text\" }, [_vm._v(\"www.nickelfox.com\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CACnDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,CAAC,EACjDH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAA<PERSON>,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EACjEJ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACxCH,GAAG,CAACK,EAAE,CAAC,iCAAiC,CAAC,CAC1C,CAAC,EACFJ,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,eAAe;IAC5BG,EAAE,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACQ;IAAgB;EACnC,CAAC,EACD,CAACR,GAAG,CAACK,EAAE,CAAC,oBAAoB,CAAC,CAC/B,CAAC,CACF,CAAC,EACFL,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,CACV,CAAC,EACFH,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA4B,CAAC,EAAE,CACtDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACK,EAAE,CAAC,qBAAqB,CAAC,CAC9B,CAAC,EACFJ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAC9CH,GAAG,CAACK,EAAE,CACJ,iFACF,CAAC,CACF,CAAC,EACFJ,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,eAAe;IAC5BG,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUE,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;EACF,CAAC,EACD,CACET,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACK,EAAE,CAAC,eAAe,CAAC,CACxB,CAAC,EACFL,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,CAEb,CAAC,CACF,CAAC,CACH,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA6B,CAAC,EAAE,CACvDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CACzCH,GAAG,CAACK,EAAE,CAAC,0BAA0B,CAAC,CACnC,CAAC,EACFJ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAC9CH,GAAG,CAACK,EAAE,CACJ,uFACF,CAAC,CACF,CAAC,EACFJ,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,eAAe;IAC5BG,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUE,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;EACF,CAAC,EACD,CACET,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACK,EAAE,CAAC,eAAe,CAAC,CACxB,CAAC,EACFL,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,CAEb,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,EACFH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA6B,CAAC,CAAC,EACxDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAA2B,CAAC,CAAC,CACvD,CAAC,EACFH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,CACV,CAAC;AACJ,CAAC;AACD,IAAIO,eAAe,GAAG,CACpB,YAAY;EACV,IAAIX,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CACnDH,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,EACdJ,EAAE,CAAC,IAAI,CAAC,EACRD,GAAG,CAACK,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIL,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACjDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAC1DJ,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,EACjEJ,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACnE,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE,UAAU;IAAES,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CACzDb,GAAG,CAACK,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFJ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE,UAAU;IAAES,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CACzDb,GAAG,CAACK,EAAE,CAAC,UAAU,CAAC,CACnB,CAAC,EACFJ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE,UAAU;IAAES,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CACzDb,GAAG,CAACK,EAAE,CAAC,cAAc,CAAC,CACvB,CAAC,EACFJ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE,UAAU;IAAES,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CACzDb,GAAG,CAACK,EAAE,CAAC,UAAU,CAAC,CACnB,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIL,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CAClDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIH,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CACnDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACxD,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIL,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CACnDF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACxD,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIL,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,QAAQ,EAAE;IAAEE,WAAW,EAAE;EAAS,CAAC,EAAE,CAC7CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACK,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAC1E,CAAC;AACJ,CAAC,CACF;AACDN,MAAM,CAACe,aAAa,GAAG,IAAI;AAE3B,SAASf,MAAM,EAAEY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}