{"ast": null, "code": "import { mapState, mapActions } from \"vuex\";\nimport axios from \"axios\";\nexport default {\n  name: \"HomePage\",\n  data() {\n    return {\n      apiData: null\n    };\n  },\n  computed: {\n    ...mapState([\"count\"])\n  },\n  methods: {\n    ...mapActions([\"increment\", \"decrement\"]),\n    async fetchData() {\n      try {\n        // 示例API请求\n        const response = await axios.get(\"https://jsonplaceholder.typicode.com/posts/1\");\n        this.apiData = response.data.title;\n        this.$message.success(\"数据获取成功!\");\n      } catch (error) {\n        this.$message.error(\"数据获取失败!\");\n        console.error(error);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapActions", "axios", "name", "data", "apiData", "computed", "methods", "fetchData", "response", "get", "title", "$message", "success", "error", "console"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\n  <div class=\"home\">\n    <el-container>\n      <el-header>\n        <h1>Vue2 项目首页</h1>\n      </el-header>\n      <el-main>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-card class=\"box-card\">\n              <div slot=\"header\" class=\"clearfix\">\n                <span>Vuex 状态管理示例</span>\n              </div>\n              <div class=\"text item\">\n                <p>当前计数: {{ count }}</p>\n                <el-button @click=\"increment\" type=\"primary\">增加</el-button>\n                <el-button @click=\"decrement\" type=\"danger\">减少</el-button>\n              </div>\n            </el-card>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-card class=\"box-card\">\n              <div slot=\"header\" class=\"clearfix\">\n                <span>Axios 请求示例</span>\n              </div>\n              <div class=\"text item\">\n                <el-button @click=\"fetchData\" type=\"success\"\n                  >获取数据</el-button\n                >\n                <p v-if=\"apiData\">{{ apiData }}</p>\n              </div>\n            </el-card>\n          </el-col>\n        </el-row>\n      </el-main>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport { mapState, mapActions } from \"vuex\";\nimport axios from \"axios\";\n\nexport default {\n  name: \"HomePage\",\n  data() {\n    return {\n      apiData: null,\n    };\n  },\n  computed: {\n    ...mapState([\"count\"]),\n  },\n  methods: {\n    ...mapActions([\"increment\", \"decrement\"]),\n    async fetchData() {\n      try {\n        // 示例API请求\n        const response = await axios.get(\n          \"https://jsonplaceholder.typicode.com/posts/1\"\n        );\n        this.apiData = response.data.title;\n        this.$message.success(\"数据获取成功!\");\n      } catch (error) {\n        this.$message.error(\"数据获取失败!\");\n        console.error(error);\n      }\n    },\n  },\n};\n</script>\n\n<style scoped>\n.home {\n  padding: 20px;\n}\n\n.box-card {\n  margin-bottom: 20px;\n}\n\n.text {\n  font-size: 14px;\n}\n\n.item {\n  margin-bottom: 18px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n.clearfix:after {\n  clear: both;\n}\n</style>\n"], "mappings": "AAwCA,SAAAA,QAAA,EAAAC,UAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAN,QAAA;EACA;EACAO,OAAA;IACA,GAAAN,UAAA;IACA,MAAAO,UAAA;MACA;QACA;QACA,MAAAC,QAAA,SAAAP,KAAA,CAAAQ,GAAA,CACA,8CACA;QACA,KAAAL,OAAA,GAAAI,QAAA,CAAAL,IAAA,CAAAO,KAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;MACA,SAAAC,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA;QACAC,OAAA,CAAAD,KAAA,CAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}