{"ast": null, "code": "export default {\n  name: \"HomePage\",\n  data() {\n    return {\n      // Data for the Save Earth page\n    };\n  },\n  methods: {\n    calculateImpact() {\n      this.$message.success(\"Calculate Impact feature coming soon!\");\n      console.log(\"Calculate Impact clicked\");\n    },\n    discoverMore(type) {\n      this.$message.info(`Discover more about ${type}`);\n      console.log(`Discover more clicked for: ${type}`);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "methods", "calculateImpact", "$message", "success", "console", "log", "discoverMore", "type", "info"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\n  <div class=\"save-earth-home\">\n    <!-- Background with gradient -->\n    <div class=\"background-gradient\"></div>\n\n    <!-- Large background text -->\n    <div class=\"background-text\">SAVE<br />EARTH</div>\n\n    <!-- Main content container -->\n    <div class=\"main-container\">\n      <!-- Navigation Header -->\n      <header class=\"navigation\">\n        <div class=\"nav-left\">\n          <span class=\"earth-emoji\">🌎</span>\n          <span class=\"brand-text\">Save Earth</span>\n          <span class=\"earth-emoji-overlay\">🌎</span>\n        </div>\n        <nav class=\"nav-right\">\n          <a href=\"#\" class=\"nav-link\">About</a>\n          <a href=\"#\" class=\"nav-link\">Articles</a>\n          <a href=\"#\" class=\"nav-link\">For Business</a>\n          <a href=\"#\" class=\"nav-link\">Showcase</a>\n        </nav>\n      </header>\n\n      <!-- Hero Section -->\n      <section class=\"hero-section\">\n        <div class=\"hero-content\">\n          <h1 class=\"hero-title\">Mother Earth</h1>\n          <p class=\"hero-subtitle\">We help you live carbon neutral</p>\n\n          <!-- Calculate Impact Button -->\n          <button class=\"calculate-btn\" @click=\"calculateImpact\">\n            Calculate Impact\n          </button>\n        </div>\n\n        <!-- Hero Image -->\n        <div class=\"hero-image\">\n          <div class=\"earth-image-container\">\n            <div class=\"earth-image\"></div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Features Section -->\n      <section class=\"features-section\">\n        <!-- Feature 1: Understand Emission -->\n        <div class=\"feature-card feature-left\">\n          <div class=\"feature-content\">\n            <h3 class=\"feature-title\">Understand Emission</h3>\n            <p class=\"feature-description\">\n              Use our calculation powered by data from world bank to estimate\n              your emission\n            </p>\n            <div class=\"discover-more\" @click=\"discoverMore('emission')\">\n              <span class=\"discover-text\">Discover More</span>\n              <div class=\"discover-button\">\n                <div class=\"arrow-icon\">→</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Feature 2: Support Climate Projects -->\n        <div class=\"feature-card feature-right\">\n          <div class=\"feature-content\">\n            <h3 class=\"feature-title\">Support Climate Projects</h3>\n            <p class=\"feature-description\">\n              Sign up and and fund high impact carbon offsetting & plastic\n              recycling initiatives.\n            </p>\n            <div class=\"discover-more\" @click=\"discoverMore('projects')\">\n              <span class=\"discover-text\">Discover More</span>\n              <div class=\"discover-button\">\n                <div class=\"arrow-icon\">→</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Decorative Elements -->\n      <div class=\"decorative-blur green-blur\"></div>\n      <div class=\"decorative-blur red-blur\"></div>\n    </div>\n\n    <!-- Footer -->\n    <footer class=\"footer\">\n      <span class=\"footer-text\">www.nickelfox.com</span>\n    </footer>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"HomePage\",\n  data() {\n    return {\n      // Data for the Save Earth page\n    };\n  },\n  methods: {\n    calculateImpact() {\n      this.$message.success(\"Calculate Impact feature coming soon!\");\n      console.log(\"Calculate Impact clicked\");\n    },\n    discoverMore(type) {\n      this.$message.info(`Discover more about ${type}`);\n      console.log(`Discover more clicked for: ${type}`);\n    },\n  },\n};\n</script>\n\n<style scoped>\n/* Main container */\n.save-earth-home {\n  position: relative;\n  width: 100vw;\n  height: 100vh;\n  overflow: hidden;\n  font-family: \"Inter\", sans-serif;\n}\n\n/* Background gradient */\n.background-gradient {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #889fa3 0%, #a5b6b9 100%);\n  z-index: 1;\n}\n\n/* Large background text */\n.background-text {\n  position: absolute;\n  top: -126px;\n  left: -39px;\n  font-family: \"Inter\", sans-serif;\n  font-weight: 700;\n  font-size: 600px;\n  line-height: 1.21;\n  letter-spacing: 7%;\n  color: rgba(0, 24, 28, 0.08);\n  z-index: 2;\n  pointer-events: none;\n}\n\n/* Main container */\n.main-container {\n  position: relative;\n  width: 1440px;\n  height: 1117px;\n  margin: 83px auto 0;\n  background: #00181c;\n  border-radius: 40px;\n  z-index: 3;\n  overflow: hidden;\n}\n\n/* Navigation */\n.navigation {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 40px 80px;\n  width: 100%;\n  box-sizing: border-box;\n}\n\n.nav-left {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  position: relative;\n}\n\n.earth-emoji {\n  font-size: 50px;\n  line-height: 1;\n}\n\n.brand-text {\n  font-family: \"Amita\", serif;\n  font-size: 32px;\n  color: #ffffff;\n  letter-spacing: 4%;\n}\n\n.earth-emoji-overlay {\n  position: absolute;\n  left: 0;\n  top: 6px;\n  font-size: 50px;\n  line-height: 1;\n}\n\n.nav-right {\n  display: flex;\n  gap: 60px;\n  align-items: center;\n}\n\n.nav-link {\n  font-family: \"Secular One\", sans-serif;\n  font-size: 16px;\n  color: #ffffff;\n  text-decoration: none;\n  letter-spacing: 4%;\n  transition: color 0.3s ease;\n}\n\n.nav-link:hover {\n  color: #1df659;\n}\n\n/* Hero Section */\n.hero-section {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 0 80px;\n  margin-top: 55px;\n}\n\n.hero-content {\n  flex: 1;\n  max-width: 500px;\n}\n\n.hero-title {\n  font-family: \"Secular One\", sans-serif;\n  font-size: 205px;\n  line-height: 1.45;\n  color: #ffffff;\n  margin: 0;\n  margin-bottom: 20px;\n}\n\n.hero-subtitle {\n  font-family: \"Inter\", sans-serif;\n  font-size: 30px;\n  line-height: 1.5;\n  color: #ffffff;\n  letter-spacing: 4%;\n  margin: 0;\n  margin-bottom: 40px;\n  max-width: 262px;\n}\n\n.calculate-btn {\n  background: #1df659;\n  border: none;\n  border-radius: 0;\n  padding: 22px 30px;\n  font-family: \"CircularXX\", sans-serif;\n  font-weight: 500;\n  font-size: 24px;\n  color: #00181c;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.calculate-btn:hover {\n  background: #16d94a;\n  transform: translateY(-2px);\n}\n\n.hero-image {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n}\n\n.earth-image-container {\n  width: 609px;\n  height: 602px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 40px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n}\n\n.earth-image {\n  width: 400px;\n  height: 400px;\n  background: radial-gradient(circle, #4caf50 0%, #2e7d32 50%, #1b5e20 100%);\n  border-radius: 50%;\n  position: relative;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\n}\n\n.earth-image::before {\n  content: \"🌍\";\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 200px;\n  opacity: 0.8;\n}\n\n/* Features Section */\n.features-section {\n  position: relative;\n  margin-top: 100px;\n  padding: 0 80px;\n}\n\n.feature-card {\n  position: absolute;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(40px);\n  padding: 40px;\n  color: #ffffff;\n}\n\n.feature-left {\n  left: 80px;\n  bottom: -200px;\n  width: 389px;\n  height: 489px;\n  border-radius: 0 89px 0 0;\n}\n\n.feature-right {\n  right: 80px;\n  bottom: -200px;\n  width: 560px;\n  height: 292px;\n  border-radius: 0 89px 0 0;\n}\n\n.feature-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.feature-title {\n  font-family: \"Inter\", sans-serif;\n  font-weight: 600;\n  font-size: 24px;\n  line-height: 1.5;\n  letter-spacing: 4%;\n  text-transform: uppercase;\n  color: #ffffff;\n  margin: 0 0 20px 0;\n}\n\n.feature-description {\n  font-family: \"Inter\", sans-serif;\n  font-size: 20px;\n  line-height: 1.5;\n  letter-spacing: 4%;\n  color: rgba(255, 255, 255, 0.8);\n  margin: 0 0 40px 0;\n  flex-grow: 1;\n}\n\n.discover-more {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.discover-more:hover {\n  transform: translateX(5px);\n}\n\n.discover-text {\n  font-family: \"Inter\", sans-serif;\n  font-size: 20px;\n  line-height: 1.5;\n  letter-spacing: 4%;\n  color: #1df659;\n}\n\n.discover-button {\n  width: 45px;\n  height: 45px;\n  background: rgba(29, 246, 89, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.discover-button::before {\n  content: \"\";\n  position: absolute;\n  width: 37px;\n  height: 37px;\n  background: #1df659;\n  border-radius: 50%;\n  top: 4px;\n  left: 4px;\n}\n\n.arrow-icon {\n  position: relative;\n  z-index: 1;\n  color: #080808;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n/* Decorative blur elements */\n.decorative-blur {\n  position: absolute;\n  filter: blur(500px);\n  pointer-events: none;\n}\n\n.green-blur {\n  width: 215px;\n  height: 415px;\n  background: #087e69;\n  left: 0;\n  bottom: 0;\n}\n\n.red-blur {\n  width: 215px;\n  height: 415px;\n  background: #d30202;\n  right: 0;\n  bottom: 200px;\n}\n\n/* Footer */\n.footer {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 351px;\n  height: 63px;\n  background: rgba(0, 0, 0, 0.11);\n  backdrop-filter: blur(100px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\n.footer-text {\n  font-family: \"Poppins\", sans-serif;\n  font-weight: 600;\n  font-size: 24px;\n  line-height: 1.6;\n  letter-spacing: 10%;\n  color: #000000;\n}\n\n/* Responsive adjustments */\n@media (max-width: 1600px) {\n  .save-earth-home {\n    width: 100%;\n    height: 100vh;\n  }\n\n  .main-container {\n    width: 90%;\n    max-width: 1440px;\n    height: auto;\n    min-height: 80vh;\n  }\n\n  .background-text {\n    font-size: 400px;\n  }\n\n  .hero-title {\n    font-size: 150px;\n  }\n\n  .feature-left,\n  .feature-right {\n    position: relative;\n    bottom: auto;\n    left: auto;\n    right: auto;\n    margin: 20px 0;\n    width: 100%;\n    max-width: 500px;\n  }\n\n  .features-section {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n    margin-top: 50px;\n    position: relative;\n  }\n}\n\n@media (max-width: 768px) {\n  .navigation {\n    flex-direction: column;\n    gap: 20px;\n    padding: 20px;\n  }\n\n  .nav-right {\n    gap: 20px;\n  }\n\n  .hero-section {\n    flex-direction: column;\n    padding: 0 20px;\n    text-align: center;\n  }\n\n  .hero-title {\n    font-size: 80px;\n  }\n\n  .hero-subtitle {\n    font-size: 20px;\n    max-width: none;\n  }\n\n  .earth-image-container {\n    width: 300px;\n    height: 300px;\n  }\n\n  .earth-image {\n    width: 200px;\n    height: 200px;\n  }\n\n  .earth-image::before {\n    font-size: 100px;\n  }\n}\n</style>\n"], "mappings": "AA+FA;EACAA,IAAA;EACAC,KAAA;IACA;MACA;IAAA,CACA;EACA;EACAC,OAAA;IACAC,gBAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;MACAC,OAAA,CAAAC,GAAA;IACA;IACAC,aAAAC,IAAA;MACA,KAAAL,QAAA,CAAAM,IAAA,wBAAAD,IAAA;MACAH,OAAA,CAAAC,GAAA,+BAAAE,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}