{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nvar _dom = require('element-ui/lib/utils/dom');\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nvar Transition = function () {\n  function Transition() {\n    _classCallCheck(this, Transition);\n  }\n  Transition.prototype.beforeEnter = function beforeEnter(el) {\n    (0, _dom.addClass)(el, 'collapse-transition');\n    if (!el.dataset) el.dataset = {};\n    el.dataset.oldPaddingTop = el.style.paddingTop;\n    el.dataset.oldPaddingBottom = el.style.paddingBottom;\n    el.style.height = '0';\n    el.style.paddingTop = 0;\n    el.style.paddingBottom = 0;\n  };\n  Transition.prototype.enter = function enter(el) {\n    el.dataset.oldOverflow = el.style.overflow;\n    if (el.scrollHeight !== 0) {\n      el.style.height = el.scrollHeight + 'px';\n      el.style.paddingTop = el.dataset.oldPaddingTop;\n      el.style.paddingBottom = el.dataset.oldPaddingBottom;\n    } else {\n      el.style.height = '';\n      el.style.paddingTop = el.dataset.oldPaddingTop;\n      el.style.paddingBottom = el.dataset.oldPaddingBottom;\n    }\n    el.style.overflow = 'hidden';\n  };\n  Transition.prototype.afterEnter = function afterEnter(el) {\n    // for safari: remove class then reset height is necessary\n    (0, _dom.removeClass)(el, 'collapse-transition');\n    el.style.height = '';\n    el.style.overflow = el.dataset.oldOverflow;\n  };\n  Transition.prototype.beforeLeave = function beforeLeave(el) {\n    if (!el.dataset) el.dataset = {};\n    el.dataset.oldPaddingTop = el.style.paddingTop;\n    el.dataset.oldPaddingBottom = el.style.paddingBottom;\n    el.dataset.oldOverflow = el.style.overflow;\n    el.style.height = el.scrollHeight + 'px';\n    el.style.overflow = 'hidden';\n  };\n  Transition.prototype.leave = function leave(el) {\n    if (el.scrollHeight !== 0) {\n      // for safari: add class after set height, or it will jump to zero height suddenly, weired\n      (0, _dom.addClass)(el, 'collapse-transition');\n      el.style.height = 0;\n      el.style.paddingTop = 0;\n      el.style.paddingBottom = 0;\n    }\n  };\n  Transition.prototype.afterLeave = function afterLeave(el) {\n    (0, _dom.removeClass)(el, 'collapse-transition');\n    el.style.height = '';\n    el.style.overflow = el.dataset.oldOverflow;\n    el.style.paddingTop = el.dataset.oldPaddingTop;\n    el.style.paddingBottom = el.dataset.oldPaddingBottom;\n  };\n  return Transition;\n}();\nexports.default = {\n  name: 'ElCollapseTransition',\n  functional: true,\n  render: function render(h, _ref) {\n    var children = _ref.children;\n    var data = {\n      on: new Transition()\n    };\n    return h('transition', data, children);\n  }\n};", "map": {"version": 3, "names": ["exports", "__esModule", "_dom", "require", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "Transition", "prototype", "beforeEnter", "el", "addClass", "dataset", "oldPaddingTop", "style", "paddingTop", "oldPaddingBottom", "paddingBottom", "height", "enter", "oldOverflow", "overflow", "scrollHeight", "afterEnter", "removeClass", "beforeLeave", "leave", "afterLeave", "default", "name", "functional", "render", "h", "_ref", "children", "data", "on"], "sources": ["C:/work/testProduct/figma/vue-project/node_modules/element-ui/lib/transitions/collapse-transition.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _dom = require('element-ui/lib/utils/dom');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Transition = function () {\n  function Transition() {\n    _classCallCheck(this, Transition);\n  }\n\n  Transition.prototype.beforeEnter = function beforeEnter(el) {\n    (0, _dom.addClass)(el, 'collapse-transition');\n    if (!el.dataset) el.dataset = {};\n\n    el.dataset.oldPaddingTop = el.style.paddingTop;\n    el.dataset.oldPaddingBottom = el.style.paddingBottom;\n\n    el.style.height = '0';\n    el.style.paddingTop = 0;\n    el.style.paddingBottom = 0;\n  };\n\n  Transition.prototype.enter = function enter(el) {\n    el.dataset.oldOverflow = el.style.overflow;\n    if (el.scrollHeight !== 0) {\n      el.style.height = el.scrollHeight + 'px';\n      el.style.paddingTop = el.dataset.oldPaddingTop;\n      el.style.paddingBottom = el.dataset.oldPaddingBottom;\n    } else {\n      el.style.height = '';\n      el.style.paddingTop = el.dataset.oldPaddingTop;\n      el.style.paddingBottom = el.dataset.oldPaddingBottom;\n    }\n\n    el.style.overflow = 'hidden';\n  };\n\n  Transition.prototype.afterEnter = function afterEnter(el) {\n    // for safari: remove class then reset height is necessary\n    (0, _dom.removeClass)(el, 'collapse-transition');\n    el.style.height = '';\n    el.style.overflow = el.dataset.oldOverflow;\n  };\n\n  Transition.prototype.beforeLeave = function beforeLeave(el) {\n    if (!el.dataset) el.dataset = {};\n    el.dataset.oldPaddingTop = el.style.paddingTop;\n    el.dataset.oldPaddingBottom = el.style.paddingBottom;\n    el.dataset.oldOverflow = el.style.overflow;\n\n    el.style.height = el.scrollHeight + 'px';\n    el.style.overflow = 'hidden';\n  };\n\n  Transition.prototype.leave = function leave(el) {\n    if (el.scrollHeight !== 0) {\n      // for safari: add class after set height, or it will jump to zero height suddenly, weired\n      (0, _dom.addClass)(el, 'collapse-transition');\n      el.style.height = 0;\n      el.style.paddingTop = 0;\n      el.style.paddingBottom = 0;\n    }\n  };\n\n  Transition.prototype.afterLeave = function afterLeave(el) {\n    (0, _dom.removeClass)(el, 'collapse-transition');\n    el.style.height = '';\n    el.style.overflow = el.dataset.oldOverflow;\n    el.style.paddingTop = el.dataset.oldPaddingTop;\n    el.style.paddingBottom = el.dataset.oldPaddingBottom;\n  };\n\n  return Transition;\n}();\n\nexports.default = {\n  name: 'ElCollapseTransition',\n  functional: true,\n  render: function render(h, _ref) {\n    var children = _ref.children;\n\n    var data = {\n      on: new Transition()\n    };\n\n    return h('transition', data, children);\n  }\n};"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,IAAI,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AAE9C,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,IAAIC,UAAU,GAAG,YAAY;EAC3B,SAASA,UAAUA,CAAA,EAAG;IACpBJ,eAAe,CAAC,IAAI,EAAEI,UAAU,CAAC;EACnC;EAEAA,UAAU,CAACC,SAAS,CAACC,WAAW,GAAG,SAASA,WAAWA,CAACC,EAAE,EAAE;IAC1D,CAAC,CAAC,EAAET,IAAI,CAACU,QAAQ,EAAED,EAAE,EAAE,qBAAqB,CAAC;IAC7C,IAAI,CAACA,EAAE,CAACE,OAAO,EAAEF,EAAE,CAACE,OAAO,GAAG,CAAC,CAAC;IAEhCF,EAAE,CAACE,OAAO,CAACC,aAAa,GAAGH,EAAE,CAACI,KAAK,CAACC,UAAU;IAC9CL,EAAE,CAACE,OAAO,CAACI,gBAAgB,GAAGN,EAAE,CAACI,KAAK,CAACG,aAAa;IAEpDP,EAAE,CAACI,KAAK,CAACI,MAAM,GAAG,GAAG;IACrBR,EAAE,CAACI,KAAK,CAACC,UAAU,GAAG,CAAC;IACvBL,EAAE,CAACI,KAAK,CAACG,aAAa,GAAG,CAAC;EAC5B,CAAC;EAEDV,UAAU,CAACC,SAAS,CAACW,KAAK,GAAG,SAASA,KAAKA,CAACT,EAAE,EAAE;IAC9CA,EAAE,CAACE,OAAO,CAACQ,WAAW,GAAGV,EAAE,CAACI,KAAK,CAACO,QAAQ;IAC1C,IAAIX,EAAE,CAACY,YAAY,KAAK,CAAC,EAAE;MACzBZ,EAAE,CAACI,KAAK,CAACI,MAAM,GAAGR,EAAE,CAACY,YAAY,GAAG,IAAI;MACxCZ,EAAE,CAACI,KAAK,CAACC,UAAU,GAAGL,EAAE,CAACE,OAAO,CAACC,aAAa;MAC9CH,EAAE,CAACI,KAAK,CAACG,aAAa,GAAGP,EAAE,CAACE,OAAO,CAACI,gBAAgB;IACtD,CAAC,MAAM;MACLN,EAAE,CAACI,KAAK,CAACI,MAAM,GAAG,EAAE;MACpBR,EAAE,CAACI,KAAK,CAACC,UAAU,GAAGL,EAAE,CAACE,OAAO,CAACC,aAAa;MAC9CH,EAAE,CAACI,KAAK,CAACG,aAAa,GAAGP,EAAE,CAACE,OAAO,CAACI,gBAAgB;IACtD;IAEAN,EAAE,CAACI,KAAK,CAACO,QAAQ,GAAG,QAAQ;EAC9B,CAAC;EAEDd,UAAU,CAACC,SAAS,CAACe,UAAU,GAAG,SAASA,UAAUA,CAACb,EAAE,EAAE;IACxD;IACA,CAAC,CAAC,EAAET,IAAI,CAACuB,WAAW,EAAEd,EAAE,EAAE,qBAAqB,CAAC;IAChDA,EAAE,CAACI,KAAK,CAACI,MAAM,GAAG,EAAE;IACpBR,EAAE,CAACI,KAAK,CAACO,QAAQ,GAAGX,EAAE,CAACE,OAAO,CAACQ,WAAW;EAC5C,CAAC;EAEDb,UAAU,CAACC,SAAS,CAACiB,WAAW,GAAG,SAASA,WAAWA,CAACf,EAAE,EAAE;IAC1D,IAAI,CAACA,EAAE,CAACE,OAAO,EAAEF,EAAE,CAACE,OAAO,GAAG,CAAC,CAAC;IAChCF,EAAE,CAACE,OAAO,CAACC,aAAa,GAAGH,EAAE,CAACI,KAAK,CAACC,UAAU;IAC9CL,EAAE,CAACE,OAAO,CAACI,gBAAgB,GAAGN,EAAE,CAACI,KAAK,CAACG,aAAa;IACpDP,EAAE,CAACE,OAAO,CAACQ,WAAW,GAAGV,EAAE,CAACI,KAAK,CAACO,QAAQ;IAE1CX,EAAE,CAACI,KAAK,CAACI,MAAM,GAAGR,EAAE,CAACY,YAAY,GAAG,IAAI;IACxCZ,EAAE,CAACI,KAAK,CAACO,QAAQ,GAAG,QAAQ;EAC9B,CAAC;EAEDd,UAAU,CAACC,SAAS,CAACkB,KAAK,GAAG,SAASA,KAAKA,CAAChB,EAAE,EAAE;IAC9C,IAAIA,EAAE,CAACY,YAAY,KAAK,CAAC,EAAE;MACzB;MACA,CAAC,CAAC,EAAErB,IAAI,CAACU,QAAQ,EAAED,EAAE,EAAE,qBAAqB,CAAC;MAC7CA,EAAE,CAACI,KAAK,CAACI,MAAM,GAAG,CAAC;MACnBR,EAAE,CAACI,KAAK,CAACC,UAAU,GAAG,CAAC;MACvBL,EAAE,CAACI,KAAK,CAACG,aAAa,GAAG,CAAC;IAC5B;EACF,CAAC;EAEDV,UAAU,CAACC,SAAS,CAACmB,UAAU,GAAG,SAASA,UAAUA,CAACjB,EAAE,EAAE;IACxD,CAAC,CAAC,EAAET,IAAI,CAACuB,WAAW,EAAEd,EAAE,EAAE,qBAAqB,CAAC;IAChDA,EAAE,CAACI,KAAK,CAACI,MAAM,GAAG,EAAE;IACpBR,EAAE,CAACI,KAAK,CAACO,QAAQ,GAAGX,EAAE,CAACE,OAAO,CAACQ,WAAW;IAC1CV,EAAE,CAACI,KAAK,CAACC,UAAU,GAAGL,EAAE,CAACE,OAAO,CAACC,aAAa;IAC9CH,EAAE,CAACI,KAAK,CAACG,aAAa,GAAGP,EAAE,CAACE,OAAO,CAACI,gBAAgB;EACtD,CAAC;EAED,OAAOT,UAAU;AACnB,CAAC,CAAC,CAAC;AAEHR,OAAO,CAAC6B,OAAO,GAAG;EAChBC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,MAAM,EAAE,SAASA,MAAMA,CAACC,CAAC,EAAEC,IAAI,EAAE;IAC/B,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAE5B,IAAIC,IAAI,GAAG;MACTC,EAAE,EAAE,IAAI7B,UAAU,CAAC;IACrB,CAAC;IAED,OAAOyB,CAAC,CAAC,YAAY,EAAEG,IAAI,EAAED,QAAQ,CAAC;EACxC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}