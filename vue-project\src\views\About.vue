<template>
  <div class="about">
    <el-container>
      <el-header>
        <h1>关于页面</h1>
      </el-header>
      <el-main>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>项目信息</span>
          </div>
          <div class="text item">
            <el-descriptions title="技术栈" :column="1" border>
              <el-descriptions-item label="前端框架">Vue 2.x</el-descriptions-item>
              <el-descriptions-item label="路由管理">Vue Router 3.x</el-descriptions-item>
              <el-descriptions-item label="状态管理">Vuex 3.x</el-descriptions-item>
              <el-descriptions-item label="HTTP客户端">Axios</el-descriptions-item>
              <el-descriptions-item label="UI组件库">Element UI</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
        
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>Element UI 组件示例</span>
          </div>
          <div class="text item">
            <el-alert
              title="成功提示"
              type="success"
              :closable="false"
              show-icon>
            </el-alert>
            <br>
            <el-button-group>
              <el-button type="primary" icon="el-icon-arrow-left">上一页</el-button>
              <el-button type="primary">下一页<i class="el-icon-arrow-right el-icon--right"></i></el-button>
            </el-button-group>
          </div>
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'About'
}
</script>

<style scoped>
.about {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
</style>
