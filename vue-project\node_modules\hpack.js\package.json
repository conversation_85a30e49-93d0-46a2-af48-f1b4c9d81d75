{"name": "hpack.js", "version": "2.1.6", "description": "HPACK implementation", "main": "lib/hpack.js", "scripts": {"test": "mocha test/*-test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/indutny/hpack.js.git"}, "keywords": ["HPACK", "HTTP2", "compress", "decompress", "headers"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/hpack.js/issues"}, "homepage": "https://github.com/indutny/hpack.js#readme", "devDependencies": {"mocha": "^2.2.5"}, "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}