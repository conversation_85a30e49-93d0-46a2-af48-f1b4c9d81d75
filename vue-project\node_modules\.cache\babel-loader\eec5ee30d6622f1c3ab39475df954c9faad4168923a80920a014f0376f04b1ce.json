{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport _extends from 'babel-runtime/helpers/extends';\nimport _typeof from 'babel-runtime/helpers/typeof';\nvar formatRegExp = /%[sdj%]/g;\nexport var warning = function warning() {};\n\n// don't print warning message when in production env or node runtime\nif (process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && typeof document !== 'undefined') {\n  warning = function warning(type, errors) {\n    if (typeof console !== 'undefined' && console.warn) {\n      if (errors.every(function (e) {\n        return typeof e === 'string';\n      })) {\n        console.warn(type, errors);\n      }\n    }\n  };\n}\nexport function format() {\n  for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  var i = 1;\n  var f = args[0];\n  var len = args.length;\n  if (typeof f === 'function') {\n    return f.apply(null, args.slice(1));\n  }\n  if (typeof f === 'string') {\n    var str = String(f).replace(formatRegExp, function (x) {\n      if (x === '%%') {\n        return '%';\n      }\n      if (i >= len) {\n        return x;\n      }\n      switch (x) {\n        case '%s':\n          return String(args[i++]);\n        case '%d':\n          return Number(args[i++]);\n        case '%j':\n          try {\n            return JSON.stringify(args[i++]);\n          } catch (_) {\n            return '[Circular]';\n          }\n          break;\n        default:\n          return x;\n      }\n    });\n    for (var arg = args[i]; i < len; arg = args[++i]) {\n      str += ' ' + arg;\n    }\n    return str;\n  }\n  return f;\n}\nfunction isNativeStringType(type) {\n  return type === 'string' || type === 'url' || type === 'hex' || type === 'email' || type === 'pattern';\n}\nexport function isEmptyValue(value, type) {\n  if (value === undefined || value === null) {\n    return true;\n  }\n  if (type === 'array' && Array.isArray(value) && !value.length) {\n    return true;\n  }\n  if (isNativeStringType(type) && typeof value === 'string' && !value) {\n    return true;\n  }\n  return false;\n}\nexport function isEmptyObject(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction asyncParallelArray(arr, func, callback) {\n  var results = [];\n  var total = 0;\n  var arrLength = arr.length;\n  function count(errors) {\n    results.push.apply(results, errors);\n    total++;\n    if (total === arrLength) {\n      callback(results);\n    }\n  }\n  arr.forEach(function (a) {\n    func(a, count);\n  });\n}\nfunction asyncSerialArray(arr, func, callback) {\n  var index = 0;\n  var arrLength = arr.length;\n  function next(errors) {\n    if (errors && errors.length) {\n      callback(errors);\n      return;\n    }\n    var original = index;\n    index = index + 1;\n    if (original < arrLength) {\n      func(arr[original], next);\n    } else {\n      callback([]);\n    }\n  }\n  next([]);\n}\nfunction flattenObjArr(objArr) {\n  var ret = [];\n  Object.keys(objArr).forEach(function (k) {\n    ret.push.apply(ret, objArr[k]);\n  });\n  return ret;\n}\nexport function asyncMap(objArr, option, func, callback) {\n  if (option.first) {\n    var flattenArr = flattenObjArr(objArr);\n    return asyncSerialArray(flattenArr, func, callback);\n  }\n  var firstFields = option.firstFields || [];\n  if (firstFields === true) {\n    firstFields = Object.keys(objArr);\n  }\n  var objArrKeys = Object.keys(objArr);\n  var objArrLength = objArrKeys.length;\n  var total = 0;\n  var results = [];\n  var next = function next(errors) {\n    results.push.apply(results, errors);\n    total++;\n    if (total === objArrLength) {\n      callback(results);\n    }\n  };\n  objArrKeys.forEach(function (key) {\n    var arr = objArr[key];\n    if (firstFields.indexOf(key) !== -1) {\n      asyncSerialArray(arr, func, next);\n    } else {\n      asyncParallelArray(arr, func, next);\n    }\n  });\n}\nexport function complementError(rule) {\n  return function (oe) {\n    if (oe && oe.message) {\n      oe.field = oe.field || rule.fullField;\n      return oe;\n    }\n    return {\n      message: oe,\n      field: oe.field || rule.fullField\n    };\n  };\n}\nexport function deepMerge(target, source) {\n  if (source) {\n    for (var s in source) {\n      if (source.hasOwnProperty(s)) {\n        var value = source[s];\n        if ((typeof value === 'undefined' ? 'undefined' : _typeof(value)) === 'object' && _typeof(target[s]) === 'object') {\n          target[s] = _extends({}, target[s], value);\n        } else {\n          target[s] = value;\n        }\n      }\n    }\n  }\n  return target;\n}", "map": {"version": 3, "names": ["_extends", "_typeof", "formatRegExp", "warning", "process", "env", "NODE_ENV", "window", "document", "type", "errors", "console", "warn", "every", "e", "format", "_len", "arguments", "length", "args", "Array", "_key", "i", "f", "len", "apply", "slice", "str", "String", "replace", "x", "Number", "JSON", "stringify", "_", "arg", "isNativeStringType", "isEmptyValue", "value", "undefined", "isArray", "isEmptyObject", "obj", "Object", "keys", "asyncParallelArray", "arr", "func", "callback", "results", "total", "arr<PERSON><PERSON><PERSON>", "count", "push", "for<PERSON>ach", "a", "asyncSerialArray", "index", "next", "original", "flatten<PERSON>bj<PERSON>rr", "obj<PERSON>rr", "ret", "k", "asyncMap", "option", "first", "flattenArr", "firstFields", "obj<PERSON><PERSON><PERSON><PERSON><PERSON>", "obj<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "complementError", "rule", "oe", "message", "field", "fullField", "deepMerge", "target", "source", "s", "hasOwnProperty"], "sources": ["C:/work/testProduct/figma/vue-project/node_modules/async-validator/es/util.js"], "sourcesContent": ["import _extends from 'babel-runtime/helpers/extends';\nimport _typeof from 'babel-runtime/helpers/typeof';\nvar formatRegExp = /%[sdj%]/g;\n\nexport var warning = function warning() {};\n\n// don't print warning message when in production env or node runtime\nif (process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && typeof document !== 'undefined') {\n  warning = function warning(type, errors) {\n    if (typeof console !== 'undefined' && console.warn) {\n      if (errors.every(function (e) {\n        return typeof e === 'string';\n      })) {\n        console.warn(type, errors);\n      }\n    }\n  };\n}\n\nexport function format() {\n  for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  var i = 1;\n  var f = args[0];\n  var len = args.length;\n  if (typeof f === 'function') {\n    return f.apply(null, args.slice(1));\n  }\n  if (typeof f === 'string') {\n    var str = String(f).replace(formatRegExp, function (x) {\n      if (x === '%%') {\n        return '%';\n      }\n      if (i >= len) {\n        return x;\n      }\n      switch (x) {\n        case '%s':\n          return String(args[i++]);\n        case '%d':\n          return Number(args[i++]);\n        case '%j':\n          try {\n            return JSON.stringify(args[i++]);\n          } catch (_) {\n            return '[Circular]';\n          }\n          break;\n        default:\n          return x;\n      }\n    });\n    for (var arg = args[i]; i < len; arg = args[++i]) {\n      str += ' ' + arg;\n    }\n    return str;\n  }\n  return f;\n}\n\nfunction isNativeStringType(type) {\n  return type === 'string' || type === 'url' || type === 'hex' || type === 'email' || type === 'pattern';\n}\n\nexport function isEmptyValue(value, type) {\n  if (value === undefined || value === null) {\n    return true;\n  }\n  if (type === 'array' && Array.isArray(value) && !value.length) {\n    return true;\n  }\n  if (isNativeStringType(type) && typeof value === 'string' && !value) {\n    return true;\n  }\n  return false;\n}\n\nexport function isEmptyObject(obj) {\n  return Object.keys(obj).length === 0;\n}\n\nfunction asyncParallelArray(arr, func, callback) {\n  var results = [];\n  var total = 0;\n  var arrLength = arr.length;\n\n  function count(errors) {\n    results.push.apply(results, errors);\n    total++;\n    if (total === arrLength) {\n      callback(results);\n    }\n  }\n\n  arr.forEach(function (a) {\n    func(a, count);\n  });\n}\n\nfunction asyncSerialArray(arr, func, callback) {\n  var index = 0;\n  var arrLength = arr.length;\n\n  function next(errors) {\n    if (errors && errors.length) {\n      callback(errors);\n      return;\n    }\n    var original = index;\n    index = index + 1;\n    if (original < arrLength) {\n      func(arr[original], next);\n    } else {\n      callback([]);\n    }\n  }\n\n  next([]);\n}\n\nfunction flattenObjArr(objArr) {\n  var ret = [];\n  Object.keys(objArr).forEach(function (k) {\n    ret.push.apply(ret, objArr[k]);\n  });\n  return ret;\n}\n\nexport function asyncMap(objArr, option, func, callback) {\n  if (option.first) {\n    var flattenArr = flattenObjArr(objArr);\n    return asyncSerialArray(flattenArr, func, callback);\n  }\n  var firstFields = option.firstFields || [];\n  if (firstFields === true) {\n    firstFields = Object.keys(objArr);\n  }\n  var objArrKeys = Object.keys(objArr);\n  var objArrLength = objArrKeys.length;\n  var total = 0;\n  var results = [];\n  var next = function next(errors) {\n    results.push.apply(results, errors);\n    total++;\n    if (total === objArrLength) {\n      callback(results);\n    }\n  };\n  objArrKeys.forEach(function (key) {\n    var arr = objArr[key];\n    if (firstFields.indexOf(key) !== -1) {\n      asyncSerialArray(arr, func, next);\n    } else {\n      asyncParallelArray(arr, func, next);\n    }\n  });\n}\n\nexport function complementError(rule) {\n  return function (oe) {\n    if (oe && oe.message) {\n      oe.field = oe.field || rule.fullField;\n      return oe;\n    }\n    return {\n      message: oe,\n      field: oe.field || rule.fullField\n    };\n  };\n}\n\nexport function deepMerge(target, source) {\n  if (source) {\n    for (var s in source) {\n      if (source.hasOwnProperty(s)) {\n        var value = source[s];\n        if ((typeof value === 'undefined' ? 'undefined' : _typeof(value)) === 'object' && _typeof(target[s]) === 'object') {\n          target[s] = _extends({}, target[s], value);\n        } else {\n          target[s] = value;\n        }\n      }\n    }\n  }\n  return target;\n}"], "mappings": ";;;;AAAA,OAAOA,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,IAAIC,YAAY,GAAG,UAAU;AAE7B,OAAO,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG,CAAC,CAAC;;AAE1C;AACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EAC7GL,OAAO,GAAG,SAASA,OAAOA,CAACM,IAAI,EAAEC,MAAM,EAAE;IACvC,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,IAAI,EAAE;MAClD,IAAIF,MAAM,CAACG,KAAK,CAAC,UAAUC,CAAC,EAAE;QAC5B,OAAO,OAAOA,CAAC,KAAK,QAAQ;MAC9B,CAAC,CAAC,EAAE;QACFH,OAAO,CAACC,IAAI,CAACH,IAAI,EAAEC,MAAM,CAAC;MAC5B;IACF;EACF,CAAC;AACH;AAEA,OAAO,SAASK,MAAMA,CAAA,EAAG;EACvB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAGC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACnFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EAEA,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAGJ,IAAI,CAAC,CAAC,CAAC;EACf,IAAIK,GAAG,GAAGL,IAAI,CAACD,MAAM;EACrB,IAAI,OAAOK,CAAC,KAAK,UAAU,EAAE;IAC3B,OAAOA,CAAC,CAACE,KAAK,CAAC,IAAI,EAAEN,IAAI,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC;EACrC;EACA,IAAI,OAAOH,CAAC,KAAK,QAAQ,EAAE;IACzB,IAAII,GAAG,GAAGC,MAAM,CAACL,CAAC,CAAC,CAACM,OAAO,CAAC3B,YAAY,EAAE,UAAU4B,CAAC,EAAE;MACrD,IAAIA,CAAC,KAAK,IAAI,EAAE;QACd,OAAO,GAAG;MACZ;MACA,IAAIR,CAAC,IAAIE,GAAG,EAAE;QACZ,OAAOM,CAAC;MACV;MACA,QAAQA,CAAC;QACP,KAAK,IAAI;UACP,OAAOF,MAAM,CAACT,IAAI,CAACG,CAAC,EAAE,CAAC,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOS,MAAM,CAACZ,IAAI,CAACG,CAAC,EAAE,CAAC,CAAC;QAC1B,KAAK,IAAI;UACP,IAAI;YACF,OAAOU,IAAI,CAACC,SAAS,CAACd,IAAI,CAACG,CAAC,EAAE,CAAC,CAAC;UAClC,CAAC,CAAC,OAAOY,CAAC,EAAE;YACV,OAAO,YAAY;UACrB;UACA;QACF;UACE,OAAOJ,CAAC;MACZ;IACF,CAAC,CAAC;IACF,KAAK,IAAIK,GAAG,GAAGhB,IAAI,CAACG,CAAC,CAAC,EAAEA,CAAC,GAAGE,GAAG,EAAEW,GAAG,GAAGhB,IAAI,CAAC,EAAEG,CAAC,CAAC,EAAE;MAChDK,GAAG,IAAI,GAAG,GAAGQ,GAAG;IAClB;IACA,OAAOR,GAAG;EACZ;EACA,OAAOJ,CAAC;AACV;AAEA,SAASa,kBAAkBA,CAAC3B,IAAI,EAAE;EAChC,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,SAAS;AACxG;AAEA,OAAO,SAAS4B,YAAYA,CAACC,KAAK,EAAE7B,IAAI,EAAE;EACxC,IAAI6B,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;IACzC,OAAO,IAAI;EACb;EACA,IAAI7B,IAAI,KAAK,OAAO,IAAIW,KAAK,CAACoB,OAAO,CAACF,KAAK,CAAC,IAAI,CAACA,KAAK,CAACpB,MAAM,EAAE;IAC7D,OAAO,IAAI;EACb;EACA,IAAIkB,kBAAkB,CAAC3B,IAAI,CAAC,IAAI,OAAO6B,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,EAAE;IACnE,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAEA,OAAO,SAASG,aAAaA,CAACC,GAAG,EAAE;EACjC,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACxB,MAAM,KAAK,CAAC;AACtC;AAEA,SAAS2B,kBAAkBA,CAACC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EAC/C,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,SAAS,GAAGL,GAAG,CAAC5B,MAAM;EAE1B,SAASkC,KAAKA,CAAC1C,MAAM,EAAE;IACrBuC,OAAO,CAACI,IAAI,CAAC5B,KAAK,CAACwB,OAAO,EAAEvC,MAAM,CAAC;IACnCwC,KAAK,EAAE;IACP,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACvBH,QAAQ,CAACC,OAAO,CAAC;IACnB;EACF;EAEAH,GAAG,CAACQ,OAAO,CAAC,UAAUC,CAAC,EAAE;IACvBR,IAAI,CAACQ,CAAC,EAAEH,KAAK,CAAC;EAChB,CAAC,CAAC;AACJ;AAEA,SAASI,gBAAgBA,CAACV,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EAC7C,IAAIS,KAAK,GAAG,CAAC;EACb,IAAIN,SAAS,GAAGL,GAAG,CAAC5B,MAAM;EAE1B,SAASwC,IAAIA,CAAChD,MAAM,EAAE;IACpB,IAAIA,MAAM,IAAIA,MAAM,CAACQ,MAAM,EAAE;MAC3B8B,QAAQ,CAACtC,MAAM,CAAC;MAChB;IACF;IACA,IAAIiD,QAAQ,GAAGF,KAAK;IACpBA,KAAK,GAAGA,KAAK,GAAG,CAAC;IACjB,IAAIE,QAAQ,GAAGR,SAAS,EAAE;MACxBJ,IAAI,CAACD,GAAG,CAACa,QAAQ,CAAC,EAAED,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLV,QAAQ,CAAC,EAAE,CAAC;IACd;EACF;EAEAU,IAAI,CAAC,EAAE,CAAC;AACV;AAEA,SAASE,aAAaA,CAACC,MAAM,EAAE;EAC7B,IAAIC,GAAG,GAAG,EAAE;EACZnB,MAAM,CAACC,IAAI,CAACiB,MAAM,CAAC,CAACP,OAAO,CAAC,UAAUS,CAAC,EAAE;IACvCD,GAAG,CAACT,IAAI,CAAC5B,KAAK,CAACqC,GAAG,EAAED,MAAM,CAACE,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC;EACF,OAAOD,GAAG;AACZ;AAEA,OAAO,SAASE,QAAQA,CAACH,MAAM,EAAEI,MAAM,EAAElB,IAAI,EAAEC,QAAQ,EAAE;EACvD,IAAIiB,MAAM,CAACC,KAAK,EAAE;IAChB,IAAIC,UAAU,GAAGP,aAAa,CAACC,MAAM,CAAC;IACtC,OAAOL,gBAAgB,CAACW,UAAU,EAAEpB,IAAI,EAAEC,QAAQ,CAAC;EACrD;EACA,IAAIoB,WAAW,GAAGH,MAAM,CAACG,WAAW,IAAI,EAAE;EAC1C,IAAIA,WAAW,KAAK,IAAI,EAAE;IACxBA,WAAW,GAAGzB,MAAM,CAACC,IAAI,CAACiB,MAAM,CAAC;EACnC;EACA,IAAIQ,UAAU,GAAG1B,MAAM,CAACC,IAAI,CAACiB,MAAM,CAAC;EACpC,IAAIS,YAAY,GAAGD,UAAU,CAACnD,MAAM;EACpC,IAAIgC,KAAK,GAAG,CAAC;EACb,IAAID,OAAO,GAAG,EAAE;EAChB,IAAIS,IAAI,GAAG,SAASA,IAAIA,CAAChD,MAAM,EAAE;IAC/BuC,OAAO,CAACI,IAAI,CAAC5B,KAAK,CAACwB,OAAO,EAAEvC,MAAM,CAAC;IACnCwC,KAAK,EAAE;IACP,IAAIA,KAAK,KAAKoB,YAAY,EAAE;MAC1BtB,QAAQ,CAACC,OAAO,CAAC;IACnB;EACF,CAAC;EACDoB,UAAU,CAACf,OAAO,CAAC,UAAUiB,GAAG,EAAE;IAChC,IAAIzB,GAAG,GAAGe,MAAM,CAACU,GAAG,CAAC;IACrB,IAAIH,WAAW,CAACI,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACnCf,gBAAgB,CAACV,GAAG,EAAEC,IAAI,EAAEW,IAAI,CAAC;IACnC,CAAC,MAAM;MACLb,kBAAkB,CAACC,GAAG,EAAEC,IAAI,EAAEW,IAAI,CAAC;IACrC;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASe,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,UAAUC,EAAE,EAAE;IACnB,IAAIA,EAAE,IAAIA,EAAE,CAACC,OAAO,EAAE;MACpBD,EAAE,CAACE,KAAK,GAAGF,EAAE,CAACE,KAAK,IAAIH,IAAI,CAACI,SAAS;MACrC,OAAOH,EAAE;IACX;IACA,OAAO;MACLC,OAAO,EAAED,EAAE;MACXE,KAAK,EAAEF,EAAE,CAACE,KAAK,IAAIH,IAAI,CAACI;IAC1B,CAAC;EACH,CAAC;AACH;AAEA,OAAO,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACxC,IAAIA,MAAM,EAAE;IACV,KAAK,IAAIC,CAAC,IAAID,MAAM,EAAE;MACpB,IAAIA,MAAM,CAACE,cAAc,CAACD,CAAC,CAAC,EAAE;QAC5B,IAAI5C,KAAK,GAAG2C,MAAM,CAACC,CAAC,CAAC;QACrB,IAAI,CAAC,OAAO5C,KAAK,KAAK,WAAW,GAAG,WAAW,GAAGrC,OAAO,CAACqC,KAAK,CAAC,MAAM,QAAQ,IAAIrC,OAAO,CAAC+E,MAAM,CAACE,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;UACjHF,MAAM,CAACE,CAAC,CAAC,GAAGlF,QAAQ,CAAC,CAAC,CAAC,EAAEgF,MAAM,CAACE,CAAC,CAAC,EAAE5C,KAAK,CAAC;QAC5C,CAAC,MAAM;UACL0C,MAAM,CAACE,CAAC,CAAC,GAAG5C,KAAK;QACnB;MACF;IACF;EACF;EACA,OAAO0C,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}